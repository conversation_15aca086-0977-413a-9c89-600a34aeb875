# luminari-source
# Source Code for LuminariMUD

The actual README and other documentation included in the original tbaMUD/CircleMUD packaging can be found here:
https://github.com/LuminariMUD/LuminariMUD/tree/master/doc

Join us on DISCORD: https://discord.gg/Me3Tuu4


------------------------------------------------------------------------------------------

Vision

Initial Vision:  To create a MUD, influenced by Pathfinder/d20/DnD3.5 rules in mechanics, integrating the code-bases:  TBA/CWG/d20.  With an original world inspired by the Bible, Dragonlance and Forgotten Realms stories.  Above all, to build a safe and friendly community of like-minded gamers to socialize and advance characters in.

A Note from the Lead Designer/Coder and Owner of LuminariMUD
	I’ve been a player/coder and builder in the MUD community for many years.  Some basic demanding traits are required to endure.  Commitment first and foremost, goal-oriented and self-motivated staff is vital as well.  Also critical is the ability to make sacrifices – for whatever reason that drives the individual, you have to be able to work through the bad times which can often outnumber the good times.  You have to, if you are a leader, lead by example.  If you are under someone else’s leadership, prove yourself to be an asset and work hard.
	Creating a MUD is not a heavily rewarding experience.  You can spend tens, hundreds even thousands of hours working on it just to get a small player-base that complains all the time.  The only real appeal is that the work itself is rewarding.  If you can adapt that attitude, then you can have a successful experience regardless of the outcome.
	As the lead on this project, I’m committed to the initial vision – and I’m committed to hard work.  Hopefully together we can make this project a success.

Luminari Staff Positions:

Website:
Website Designer/Manager (Blog)		Staff/Senior Staff
Website Designer/Manager (Forum)		Staff/Senior Staff

Helpfiles:
Help File Lead					Staff/Senior Staff
Help File Creator/Editor(s)			Staff/Senior Staff

World and Lore:
Lead World Design				Forger
Lead Lore Design				Forger
Lead Scripter					Forger
Quest Designer(s)				Staff/Senior Staff
Builder(s)					Staff/Senior Staff

Code, Mechanics Designer:
Lead Programmer				Forger
Lead Game Designer				Forger
Programmer(s)				Staff/Senior Staff

Game Administration:
Lead Administrator				Forger
Administrator(s)				Staff/Senior Staff
Recruiter / 101					Senior Staff / Forger

Staff Position Description

Website Designer/Manager (Blog)
Responsibilities:
To create, design and manage all content of the blog aspect of the website.  Content will be at the very least minimum weekly updates of code changes.
Limitations:
	Maintenance of the Web Forum	

Website Designer/Manager (Forum)
Responsibilities:  
To create, design and manage all content of the forum aspect of the website.  Content includes all community discussion, and will require maintenance, organization of content, and other responsibilities.
Limitations:
	Maintenance of the Web Blog	

Help File Lead:
Responsibilities:  
	To organize a system for keyword content of help files.  Organizing and designing the primary ‘HELP’ command formatting.  Add help files as needed.  Needs to be in good communication with programmers on understanding the content that gets added to the game.
Limitations:
	No building responsibilities

Help File Creator/Editor(s)
Responsibilities:  
	Adding help-file content for any conceivable topic that would assist player in navigating the MUD
Limitations:
	
Lead World Design (Head Builder)
Responsibilities:
	Design maps and layout of lands, attach zones in appropriate areas.  Establish building-standards, rules and stylistics.  In charge of making sure world creation meets goals in a timely manner.  Assist new builders in building and scripting.
Limitations:
	Expected to create the world with the tools provided and to maintain the TBA codebase standards for building techniques.
	
Lead Lore Design
Responsibilities:  
	Designs all aspects within the realm of game-lore.  Including stories, world background information, world-inhabitants relationship, etc.
Limitations:
	Creating lore that isn’t consistent with the base rules of content:  There is only one true G-d in Luminari lore.  Also to stay within the realm of reasonable Dungeons-and-Dragons content.

Quest Designer(s)
Responsibilities:  
	Take leadership in making sure quests are consistent in rewards and that they match with the Lore content.
Limitations:
	Creating ALL the quest content, the responsibility lies on the builders 	

Recruiter / 101
Responsibilities: 
	To be liaison in the MUDding community forums.  To actively post recruiting messages to the MUDding community, but not be obnoxious about it.  To help start off new staff members and ease the heavy training load of Lead Builder.   
Limitations:
	For building aspects of the job, under jurisdiction of Lead Builder

Builder(s)
Responsibilities: 
	To create the world, scripts and quests for players to explore. 
Limitations:
	Under jurisdiction of Lead Builder

Lead Scripter
Responsibilities: 
	To create universal scripts for the builders to use, to help support and answer questions about scripts
Limitations:

Lead Programmer
Responsibilities:
	Divide amongst the coders work-load.  Oversee consistency in code-content that enters the Subversion control.  Manage backups and SVN content.  Makes final decisions on technique for implementation and coding-standards.
Limitations:
	Authority limited to code

Lead Game Designer
Responsibilities:  
	Decide game rules for in-game systems and mechanics.  Overall vision for project direction.  Management of project to achieve goals in timely manner.
Limitations:	
	Authority doesn’t include building-standards, lore or deciding coding technique.  Also not an administrator.

Programmer(s)
Responsibilities:  
	Implement game mechanics.
Limitations:
	Answers to Lead-Programmer, authority limited to code.

Lead Administrator
Responsibilities:  
	Lead management of administrators.  Maintain peace between staff and players, implement rules-of-conduct for player and staff behaviour.
Limitations:
	The rules of behaviour have to be consistent with Judeo-Christian ideals:  Do not do unto others that you don’t want done to yourself.

Administrator(s)
Responsibilities: 
	Assist in enforcement of conduct rules between players and players, staff and staff.  Invest time and patience in new players (newbies).
Limitations:
	Under management of Lead Administrator

General Guidelines of Staff Conduct
(The Luminari Constitution)

Initial ideas for the world are:
	-Worldmap for navigation between zones
	-Allows for expansion of system including vehicles, etc
	-Heavily quest and story orientated progression
	-Keeps players involved to see advancement of a story
	-Heavy usage of scripting to create a “living and breathing” world
	-Stock zones can only be kept if redesigned
	-Keeps content high
	-Keeps mud-stats high
	-Allows some familiar zones for those who have affectionate memories of some of the stock zones
  		** We can take out zones as they get replaced with new ones if the lead builder desires
	-The goals for Beta in mechanics and code have already been established.  The allowance for change in direction is limited due to time-related goals.  After Beta, major changes in system CAN be considered.
	-Very simple to understand the mechanic goals:  view any Pathfinder/d20/DnD3.5 rules website to get the idea
	-The lead designer is choosing what to implement and what not to implement based on
		--Ease
		--Past experience on what systems work
		--Fun-factor
	-Ideas coming from the staff should be well thought out to avoid clutter.  If you have a lot of ideas, it’s best to start a Forum-Topic and post them under your topic, then go to town
	-Bugs and Typos do NOT need to be thought out at all, if you even slightly think something is  a Bug/Typo, do not be afraid to report it
	-Builders or Builder’s Leadership should NOT try and change the standards and system established by TBA – that would destroy our primary community of potential builders.
		--One of the big advantages of using TBA codebase is the active community
	-It’s OK to discuss with other leadership ideas in their field of leadership, but remember whose field of leadership it is… and AVOID arguing if that leadership makes a decision; examples:
	-If you are the Lead Designer or Lead Builder, don’t try to trump the Lead Admin (for example in establishment of in-game player-to-player rules)
	-If you are the Lead Designer or Lead Admin, don’t submit lore or content that isn’t consistent with the Lead Builder/Lore
	-If you are the Lead Builder/Admin, don’t try to insist on game mechanics or design of OLC interface, etc
	-Try to be at least a little respectful and sensitive to any individuals who are committing a lot more of their effort/work into the MUD
	-Do NOT take other’s time for granted, if you ask a coder to do something that takes 10 hours of his time, you should be prepared to do that much work in return for the coder
	-Although the Lead-Designer does tend to heavily implement ideas submitted by the community, the default expectation is quite simple:  we do NOT customize the MUD to individual preferences.


Conflict of Interest (Leaders Only)
	-You can NOT hold a position of leadership in Luminari if you are committed more or equally to another MUD.  This includes, but is not limited to, being the leader of a very active guild on another MUD.
	-You can NOT hold a position of leadership in Luminari if you are in any sort of conflicting relationship with any other game as per discretion of the Lead Administrator


Minimal Log-In Time (All Staff)
	-6 weeks on inactivity with no contact can result in demotion


Minimal Knowledge (Leaders Only)
	-How can one lead a MUD they know nothing about?
	-All Leaders are required to be familiar with all base races/classes and MUD lore
	-Leadership positions individuals are expected to lead by example
	-Life comes first, but if you can’t handle the responsibilities and time investment of leadership, you should try to find a replacement
	-If you can’t invest minimal amount of time to work on the MUD as a leader, you will be politely placed in a non-leadership role


Leadership Positions MINIMUM Required Work:
	-Building:  3 “things” per week, see Lead Builder for details
	-Admin:  X hours of active time on the MUD, see Lead Admin

ANYONE has a right to request the code

ANYONE can request a copy of their zone at any time, but see the on-line DISCLAIMER help file for details on the MUD’s rights with the builders zones

REMEMBER:  Ultimately, if push comes to shove, the lead does have a right to trump decisions that aren’t in-line with the initial vision.  Or if he knows the idea to be ineffective based on past MUDding experience.

