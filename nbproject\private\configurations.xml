
<?xml version="1.0" encoding="UTF-8"?>
<configurationDescriptor version="91">
  <logicalFolder name="root" displayName="root" projectFiles="true" kind="ROOT">
    <df root="." name="0">
      <df name="rtree">
        <in>card.c</in>
        <in>card.h</in>
        <in>index.c</in>
        <in>node.c</in>
        <in>rTreeIndex.h</in>
        <in>rectangle.c</in>
        <in>split_l.c</in>
        <in>split_l.h</in>
      </df>
      <df name="util">
        <in>asciipasswd.c</in>
        <in>autowiz.c</in>
        <in>hl_events.c</in>
        <in>hl_events.h</in>
        <in>plrtoascii.c</in>
        <in>rebuildAsciiIndex.c</in>
        <in>rebuildMailIndex.c</in>
        <in>shopconv.c</in>
        <in>sign.c</in>
        <in>split.c</in>
        <in>webster.c</in>
        <in>wld2html.c</in>
      </df>
      <in>account.c</in>
      <in>act.comm.c</in>
      <in>act.h</in>
      <in>act.informative.c</in>
      <in>act.item.c</in>
      <in>act.movement.c</in>
      <in>act.offensive.c</in>
      <in>act.other.c</in>
      <in>act.social.c</in>
      <in>act.wizard.c</in>
      <in>actionqueues.c</in>
      <in>actionqueues.h</in>
      <in>actions.c</in>
      <in>actions.h</in>
      <in>aedit.c</in>
      <in>asciimap.c</in>
      <in>asciimap.h</in>
      <in>ban.c</in>
      <in>ban.h</in>
      <in>boards.c</in>
      <in>boards.h</in>
      <in>bsd-snprintf.c</in>
      <in>bsd-snprintf.h</in>
      <in>cedit.c</in>
      <in>clan.c</in>
      <in>clan.h</in>
      <in>clan_edit.c</in>
      <in>class.c</in>
      <in>class.h</in>
      <in>combat_modes.c</in>
      <in>combat_modes.h</in>
      <in>comm.c</in>
      <in>comm.h</in>
      <in>conf.h</in>
      <in>config.c</in>
      <in>config.h</in>
      <in>constants.c</in>
      <in>constants.h</in>
      <in>craft.c</in>
      <in>craft.h</in>
      <in>db.c</in>
      <in>db.h</in>
      <in>desc_engine.c</in>
      <in>dg_comm.c</in>
      <in>dg_db_scripts.c</in>
      <in>dg_event.c</in>
      <in>dg_event.h</in>
      <in>dg_handler.c</in>
      <in>dg_misc.c</in>
      <in>dg_mobcmd.c</in>
      <in>dg_objcmd.c</in>
      <in>dg_olc.c</in>
      <in>dg_olc.h</in>
      <in>dg_scripts.c</in>
      <in>dg_scripts.h</in>
      <in>dg_triggers.c</in>
      <in>dg_variables.c</in>
      <in>dg_wldcmd.c</in>
      <in>feats.c</in>
      <in>feats.h</in>
      <in>fight.c</in>
      <in>fight.h</in>
      <in>gain.c</in>
      <in>genmob.c</in>
      <in>genmob.h</in>
      <in>genobj.c</in>
      <in>genobj.h</in>
      <in>genolc.c</in>
      <in>genolc.h</in>
      <in>genqst.c</in>
      <in>genshp.c</in>
      <in>genshp.h</in>
      <in>genwld.c</in>
      <in>genwld.h</in>
      <in>genzon.c</in>
      <in>genzon.h</in>
      <in>graph.c</in>
      <in>graph.h</in>
      <in>handler.c</in>
      <in>handler.h</in>
      <in>hedit.c</in>
      <in>hedit.h</in>
      <in>help.c</in>
      <in>help.h</in>
      <in>hlqedit.c</in>
      <in>hlquest.c</in>
      <in>hlquest.h</in>
      <in>house.c</in>
      <in>house.h</in>
      <in>ibt.c</in>
      <in>ibt.h</in>
      <in>improved-edit.c</in>
      <in>improved-edit.h</in>
      <in>interpreter.c</in>
      <in>interpreter.h</in>
      <in>kdtree.c</in>
      <in>kdtree.h</in>
      <in>limits.c</in>
      <in>lists.c</in>
      <in>lists.h</in>
      <in>magic.c</in>
      <in>mail.c</in>
      <in>mail.h</in>
      <in>medit.c</in>
      <in>memorize.c</in>
      <in>mobact.c</in>
      <in>mobact.h</in>
      <in>modify.c</in>
      <in>modify.h</in>
      <in>msgedit.c</in>
      <in>msgedit.h</in>
      <in>mud_event.c</in>
      <in>mud_event.h</in>
      <in>mudlim.h</in>
      <in>mysql.c</in>
      <in>mysql.h</in>
      <in>oasis.c</in>
      <in>oasis.h</in>
      <in>oasis_copy.c</in>
      <in>oasis_delete.c</in>
      <in>oasis_list.c</in>
      <in>objsave.c</in>
      <in>oedit.c</in>
      <in>perlin.c</in>
      <in>perlin.h</in>
      <in>pfdefaults.h</in>
      <in>players.c</in>
      <in>prefedit.c</in>
      <in>prefedit.h</in>
      <in>protocol.c</in>
      <in>protocol.h</in>
      <in>qedit.c</in>
      <in>quest.c</in>
      <in>quest.h</in>
      <in>race.c</in>
      <in>race.h</in>
      <in>random.c</in>
      <in>redit.c</in>
      <in>screen.h</in>
      <in>sedit.c</in>
      <in>shop.c</in>
      <in>shop.h</in>
      <in>spec_abilities.c</in>
      <in>spec_abilities.h</in>
      <in>spec_assign.c</in>
      <in>spec_procs.c</in>
      <in>spec_procs.h</in>
      <in>spell_parser.c</in>
      <in>spells.c</in>
      <in>spells.h</in>
      <in>structs.h</in>
      <in>study.c</in>
      <in>sysdep.h</in>
      <in>tedit.c</in>
      <in>telnet.h</in>
      <in>traps.c</in>
      <in>traps.h</in>
      <in>treasure.c</in>
      <in>treasure.h</in>
      <in>treasure_const.c</in>
      <in>utils.c</in>
      <in>utils.h</in>
      <in>weather.c</in>
      <in>wilderness.c</in>
      <in>wilderness.h</in>
      <in>zedit.c</in>
      <in>zmalloc.c</in>
      <in>zmalloc.h</in>
      <in>zone_procs.c</in>
    </df>
  </logicalFolder>
  <projectmakefile>Makefile</projectmakefile>
  <confs>
    <conf name="Default" type="0">
      <toolsSet>
        <developmentServer>localhost</developmentServer>
        <platform>3</platform>
      </toolsSet>
      <compile>
        <compiledirpicklist>
          <compiledirpicklistitem>.</compiledirpicklistitem>
          <compiledirpicklistitem>${AUTO_FOLDER}</compiledirpicklistitem>
        </compiledirpicklist>
        <compiledir>${AUTO_FOLDER}</compiledir>
        <compilecommandpicklist>
          <compilecommandpicklistitem>${MAKE} ${ITEM_NAME}.o</compilecommandpicklistitem>
          <compilecommandpicklistitem>${AUTO_COMPILE}</compilecommandpicklistitem>
        </compilecommandpicklist>
        <compilecommand>${AUTO_COMPILE}</compilecommand>
      </compile>
      <dbx_gdbdebugger version="1">
        <gdb_pathmaps>
        </gdb_pathmaps>
        <gdb_interceptlist>
          <gdbinterceptoptions gdb_all="false" gdb_unhandled="true" gdb_unexpected="true"/>
        </gdb_interceptlist>
        <gdb_options>
          <DebugOptions>
          </DebugOptions>
        </gdb_options>
        <gdb_buildfirst gdb_buildfirst_overriden="false" gdb_buildfirst_old="false"/>
      </dbx_gdbdebugger>
      <nativedebugger version="1">
        <engine>gdb</engine>
      </nativedebugger>
      <runprofile version="9">
        <runcommandpicklist>
          <runcommandpicklistitem>"${OUTPUT_PATH}"</runcommandpicklistitem>
        </runcommandpicklist>
        <runcommand>"${OUTPUT_PATH}"</runcommand>
        <rundir>.</rundir>
        <buildfirst>true</buildfirst>
        <terminal-type>0</terminal-type>
        <remove-instrumentation>0</remove-instrumentation>
        <environment>
        </environment>
      </runprofile>
    </conf>
  </confs>
</configurationDescriptor>
