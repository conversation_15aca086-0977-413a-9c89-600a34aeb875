// New LuminariMUD Crafting System Recipes by <PERSON> aka <PERSON><PERSON>

#include "conf.h"
#include "sysdep.h"
#include "structs.h"
#include "mysql.h"
#include "utils.h"
#include "comm.h"
#include "spells.h"
#include "interpreter.h"
#include "constants.h"
#include "handler.h"
#include "db.h"
#include "craft.h"
#include "spells.h"
#include "mud_event.h"
#include "modify.h"
#include "treasure.h"
#include "mudlim.h"
#include "spec_procs.h"
#include "item.h"
#include "quest.h"
#include "assign_wpn_armor.h"
#include "genolc.h"
#include "crafting_new.h"
#include "crafting_recipes.h"

struct craft_recipe_data crafting_recipes[NUM_CRAFTING_RECIPES];
struct refine_recipe_data refining_recipes[NUM_REFINING_RECIPES];

void craft_recipe(int rec, int obj_type, int obj_subtype, const char *name, int practical)
{
    crafting_recipes[rec].name = name;
    crafting_recipes[rec].object_type = obj_type;
    crafting_recipes[rec].object_subtype = obj_subtype;
    crafting_recipes[rec].practical_type = practical;
}

void cvariant(int rec, int var, int skill, int mat_one, int num_one, int mat_two, int num_two, int mat_three, int num_three, const char *desc)
{
    crafting_recipes[rec].variant_skill[var] = skill;
    crafting_recipes[rec].materials[0][var][0] = mat_one;
    crafting_recipes[rec].materials[0][var][1] = num_one;
    crafting_recipes[rec].materials[1][var][0] = mat_two;
    crafting_recipes[rec].materials[1][var][1] = num_two;
    crafting_recipes[rec].materials[2][var][0] = mat_three;
    crafting_recipes[rec].materials[2][var][1] = num_three;
    crafting_recipes[rec].variant_descriptions[var] = desc;
}

void refine_recipe(int recipe, int skill, int dc, int mat_one, int amount_one, int mat_two, int amount_two, int mat_three, int amount_three, int result, int result_amount, int flag)
{
    refining_recipes[recipe].skill = skill;
    refining_recipes[recipe].dc = dc;
    refining_recipes[recipe].materials[0][0] = mat_one;
    refining_recipes[recipe].materials[0][1] = amount_one;
    refining_recipes[recipe].materials[1][0] = mat_two;
    refining_recipes[recipe].materials[1][1] = amount_two;
    refining_recipes[recipe].materials[2][0] = mat_three;
    refining_recipes[recipe].materials[2][1] = amount_three;
    refining_recipes[recipe].result[0] = result;
    refining_recipes[recipe].result[1] = result_amount;
    refining_recipes[recipe].crafting_station_flag = flag;
}

void initialize_refining_recipes(void)
{
    int i = 0;

    for (i = 0; i < NUM_REFINING_RECIPES; i++)
    {
        refining_recipes[i].skill = 0;
        refining_recipes[i].dc = 0;
        refining_recipes[i].materials[0][0] = 0;
        refining_recipes[i].materials[0][1] = 0;
        refining_recipes[i].materials[1][0] = 0;
        refining_recipes[i].materials[1][1] = 0;
        refining_recipes[i].materials[2][0] = 0;
        refining_recipes[i].materials[2][1] = 0;
        refining_recipes[i].result[0] = 0;
        refining_recipes[i].result[1] = 0;
        refining_recipes[i].crafting_station_flag = 0;
    }
}

void initialize_crafting_recipes(void)
{
  int i, j, k;

  /* initialize the list of encounters */
  for (i = 0; i < NUM_CRAFTING_RECIPES; i++)
  {
    crafting_recipes[i].name = "unused";
    crafting_recipes[i].object_type = 0;
    crafting_recipes[i].object_subtype = 0;
    crafting_recipes[i].practical_type = 0;
    for (j = 0; j < NUM_CRAFT_VARIANTS; j++)
    {
        crafting_recipes[i].variant_skill[j] = 0;
        crafting_recipes[i].variant_descriptions[j] = "none";
        for (k = 0; k < 2; k++)
        {
            crafting_recipes[i].materials[0][j][k] = 0;
            crafting_recipes[i].materials[1][j][k] = 0;
            crafting_recipes[i].materials[2][j][k] = 0;
        }
    }
  }
}

void populate_refining_recipes(void)
{
    initialize_refining_recipes();

    refine_recipe(REFINE_RECIPE_BRONZE, ABILITY_HARVEST_MINING, 15, CRAFT_MAT_TIN, 1, CRAFT_MAT_COPPER, 1, 0, 0, CRAFT_MAT_BRONZE, 1, ITEM_CRAFTING_SMELTER);
    refine_recipe(REFINE_RECIPE_STEEL, ABILITY_HARVEST_MINING, 20, CRAFT_MAT_IRON, 1, CRAFT_MAT_COAL, 1, 0, 0, CRAFT_MAT_STEEL, 1, ITEM_CRAFTING_SMELTER);
    refine_recipe(REFINE_RECIPE_BRASS, ABILITY_HARVEST_MINING, 15, CRAFT_MAT_COPPER, 1, CRAFT_MAT_ZINC, 1, 0, 0, CRAFT_MAT_BRASS, 2, ITEM_CRAFTING_SMELTER);
    refine_recipe(REFINE_RECIPE_SATIN, ABILITY_CRAFT_TAILORING, 30, CRAFT_MAT_SILK, 1, CRAFT_MAT_COTTON, 1, 0, 0, CRAFT_MAT_SATIN, 1, ITEM_CRAFTING_LOOM);
    refine_recipe(REFINE_RECIPE_LINEN, ABILITY_CRAFT_TAILORING, 20, CRAFT_MAT_FLAX, 2, 0, 0, 0, 0, CRAFT_MAT_LINEN, 2, ITEM_CRAFTING_LOOM);
    refine_recipe(REFINE_RECIPE_ALCHEMICAL_SILVER, ABILITY_HARVEST_MINING, 25, CRAFT_MAT_STEEL, 2, CRAFT_MAT_SILVER, 2, 0, 0, CRAFT_MAT_ALCHEMAL_SILVER, 1, ITEM_CRAFTING_SMELTER);
    refine_recipe(REFINE_RECIPE_COLD_IRON, ABILITY_HARVEST_MINING, 25, CRAFT_MAT_IRON, 2, CRAFT_MAT_ZINC, 1, CRAFT_MAT_SILVER, 1, CRAFT_MAT_COLD_IRON, 1, ITEM_CRAFTING_SMELTER);
}

void populate_crafting_recipes(void)
{

    initialize_crafting_recipes();

    craft_recipe(CRAFT_RECIPE_WEAPON_UNARMED, ITEM_WEAPON, WEAPON_TYPE_UNARMED, "unarmed", WEAPON_TYPE_UNARMED);
        cvariant(CRAFT_RECIPE_WEAPON_UNARMED, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "cestus");
        cvariant(CRAFT_RECIPE_WEAPON_UNARMED, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "claws");
        cvariant(CRAFT_RECIPE_WEAPON_UNARMED, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, 0, 0, 0, 0, "knuckle");
    craft_recipe(CRAFT_RECIPE_WEAPON_DAGGER, ITEM_WEAPON, WEAPON_TYPE_DAGGER, "dagger", WEAPON_TYPE_DAGGER);
        cvariant(CRAFT_RECIPE_WEAPON_DAGGER, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "dagger");
        cvariant(CRAFT_RECIPE_WEAPON_DAGGER, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "dirk");
        cvariant(CRAFT_RECIPE_WEAPON_DAGGER, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "blade");
        cvariant(CRAFT_RECIPE_WEAPON_DAGGER, 3, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "kris");
        cvariant(CRAFT_RECIPE_WEAPON_DAGGER, 4, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "stiletto");
        cvariant(CRAFT_RECIPE_WEAPON_DAGGER, 5, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "baselard");
        cvariant(CRAFT_RECIPE_WEAPON_DAGGER, 6, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "main gauche");
    craft_recipe(CRAFT_RECIPE_WEAPON_LIGHT_MACE, ITEM_WEAPON, WEAPON_TYPE_LIGHT_MACE, "light mace", WEAPON_TYPE_LIGHT_MACE);
        cvariant(CRAFT_RECIPE_WEAPON_LIGHT_MACE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "light mace");
    craft_recipe(CRAFT_RECIPE_WEAPON_SICKLE, ITEM_WEAPON, WEAPON_TYPE_SICKLE, "sickle", WEAPON_TYPE_SICKLE);
        cvariant(CRAFT_RECIPE_WEAPON_SICKLE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "sickle");
    craft_recipe(CRAFT_RECIPE_WEAPON_CLUB, ITEM_WEAPON, WEAPON_TYPE_CLUB, "club", WEAPON_TYPE_CLUB);
        cvariant(CRAFT_RECIPE_WEAPON_CLUB, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "club");
        cvariant(CRAFT_RECIPE_WEAPON_CLUB, 1, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "cudgel");
        cvariant(CRAFT_RECIPE_WEAPON_CLUB, 2, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "baton");
    craft_recipe(CRAFT_RECIPE_WEAPON_HEAVY_MACE, ITEM_WEAPON, WEAPON_TYPE_HEAVY_MACE, "heavy mace", WEAPON_TYPE_HEAVY_MACE);
        cvariant(CRAFT_RECIPE_WEAPON_HEAVY_MACE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "heavy mace");
    craft_recipe(CRAFT_RECIPE_WEAPON_MORNINGSTAR, ITEM_WEAPON, WEAPON_TYPE_MORNINGSTAR, "morningstar", WEAPON_TYPE_MORNINGSTAR);
        cvariant(CRAFT_RECIPE_WEAPON_MORNINGSTAR, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "morningstar");
    craft_recipe(CRAFT_RECIPE_WEAPON_SHORTSPEAR, ITEM_WEAPON, WEAPON_TYPE_SHORTSPEAR, "shortspear", WEAPON_TYPE_SHORTSPEAR);
        cvariant(CRAFT_RECIPE_WEAPON_SHORTSPEAR, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_HARD_METALS, 3, "shortspear");
    craft_recipe(CRAFT_RECIPE_WEAPON_LONGSPEAR, ITEM_WEAPON, WEAPON_TYPE_LONGSPEAR, "longspear", WEAPON_TYPE_LONGSPEAR);
        cvariant(CRAFT_RECIPE_WEAPON_LONGSPEAR, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 7, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_HARD_METALS, 3, "longspear");
    craft_recipe(CRAFT_RECIPE_WEAPON_QUARTERSTAFF, ITEM_WEAPON, WEAPON_TYPE_QUARTERSTAFF, "quarterstaff", WEAPON_TYPE_QUARTERSTAFF);
        cvariant(CRAFT_RECIPE_WEAPON_QUARTERSTAFF, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 7, CRAFT_GROUP_HIDES, 2, 0, 0, "quarterstaff");
        cvariant(CRAFT_RECIPE_WEAPON_QUARTERSTAFF, 1, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 7, CRAFT_GROUP_HIDES, 2, 0, 0, "staff");
    craft_recipe(CRAFT_RECIPE_WEAPON_SPEAR, ITEM_WEAPON, WEAPON_TYPE_SPEAR, "spear", WEAPON_TYPE_SPEAR);
        cvariant(CRAFT_RECIPE_WEAPON_SPEAR, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_HARD_METALS, 3, "spear");
    craft_recipe(CRAFT_RECIPE_WEAPON_HEAVY_CROSSBOW, ITEM_WEAPON, WEAPON_TYPE_HEAVY_CROSSBOW, "heavy crossbow", WEAPON_TYPE_HEAVY_CROSSBOW);
        cvariant(CRAFT_RECIPE_WEAPON_HEAVY_CROSSBOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 6, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_CLOTH, 1, "heavy crossbow");
    craft_recipe(CRAFT_RECIPE_WEAPON_LIGHT_CROSSBOW, ITEM_WEAPON, WEAPON_TYPE_LIGHT_CROSSBOW, "light crossbow", WEAPON_TYPE_LIGHT_CROSSBOW);
        cvariant(CRAFT_RECIPE_WEAPON_LIGHT_CROSSBOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_CLOTH, 1, "light crossbow");
    craft_recipe(CRAFT_RECIPE_WEAPON_DART, ITEM_WEAPON, WEAPON_TYPE_DART, "dart", WEAPON_TYPE_DART);
        cvariant(CRAFT_RECIPE_WEAPON_DART, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 1, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_HARD_METALS, 1, "dart");
    craft_recipe(CRAFT_RECIPE_WEAPON_JAVELIN, ITEM_WEAPON, WEAPON_TYPE_JAVELIN, "javelin", WEAPON_TYPE_JAVELIN);
        cvariant(CRAFT_RECIPE_WEAPON_JAVELIN, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_HARD_METALS, 2, "javelin");
    craft_recipe(CRAFT_RECIPE_WEAPON_SLING, ITEM_WEAPON, WEAPON_TYPE_SLING, "sling", WEAPON_TYPE_SLING);
        cvariant(CRAFT_RECIPE_WEAPON_SLING, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 3, CRAFT_GROUP_CLOTH, 1, 0, 0, "sling");
    craft_recipe(CRAFT_RECIPE_WEAPON_THROWING_AXE, ITEM_WEAPON, WEAPON_TYPE_THROWING_AXE, "throwing axe", WEAPON_TYPE_THROWING_AXE);
        cvariant(CRAFT_RECIPE_WEAPON_THROWING_AXE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_WOOD, 1, CRAFT_GROUP_HIDES, 1, "throwing axe");
    craft_recipe(CRAFT_RECIPE_WEAPON_LIGHT_HAMMER, ITEM_WEAPON, WEAPON_TYPE_LIGHT_HAMMER, "light hammer", WEAPON_TYPE_LIGHT_HAMMER);
        cvariant(CRAFT_RECIPE_WEAPON_LIGHT_HAMMER, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_WOOD, 1, CRAFT_GROUP_HIDES, 1, "light hammer");
    craft_recipe(CRAFT_RECIPE_WEAPON_HAND_AXE, ITEM_WEAPON, WEAPON_TYPE_HAND_AXE, "hand axe", WEAPON_TYPE_HAND_AXE);
        cvariant(CRAFT_RECIPE_WEAPON_HAND_AXE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_WOOD, 1, CRAFT_GROUP_HIDES, 1, "hand axe");
    craft_recipe(CRAFT_RECIPE_WEAPON_KUKRI, ITEM_WEAPON, WEAPON_TYPE_KUKRI, "kukri", WEAPON_TYPE_KUKRI);
        cvariant(CRAFT_RECIPE_WEAPON_KUKRI, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "kukri");
        cvariant(CRAFT_RECIPE_WEAPON_KUKRI, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "tanto");
    craft_recipe(CRAFT_RECIPE_WEAPON_LIGHT_PICK, ITEM_WEAPON, WEAPON_TYPE_LIGHT_PICK, "light pick", WEAPON_TYPE_LIGHT_PICK);
        cvariant(CRAFT_RECIPE_WEAPON_LIGHT_PICK, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_WOOD, 2, CRAFT_GROUP_HIDES, 1, "light pick");
    craft_recipe(CRAFT_RECIPE_WEAPON_SAP, ITEM_WEAPON, WEAPON_TYPE_SAP, "sap", WEAPON_TYPE_SAP);
        cvariant(CRAFT_RECIPE_WEAPON_SAP, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "sap");
        cvariant(CRAFT_RECIPE_WEAPON_SAP, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "blackjack");
    craft_recipe(CRAFT_RECIPE_WEAPON_SHORT_SWORD, ITEM_WEAPON, WEAPON_TYPE_SHORT_SWORD, "short sword", WEAPON_TYPE_SHORT_SWORD);
        cvariant(CRAFT_RECIPE_WEAPON_SHORT_SWORD, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "short sword");
        cvariant(CRAFT_RECIPE_WEAPON_SHORT_SWORD, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "gladius");
        cvariant(CRAFT_RECIPE_WEAPON_SHORT_SWORD, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "xiphos");
        cvariant(CRAFT_RECIPE_WEAPON_SHORT_SWORD, 3, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "spatha");
        cvariant(CRAFT_RECIPE_WEAPON_SHORT_SWORD, 4, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "wakizashi");
        cvariant(CRAFT_RECIPE_WEAPON_SHORT_SWORD, 5, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "cutlass");
        cvariant(CRAFT_RECIPE_WEAPON_SHORT_SWORD, 6, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "machete");
    craft_recipe(CRAFT_RECIPE_WEAPON_BATTLE_AXE, ITEM_WEAPON, WEAPON_TYPE_BATTLE_AXE, "battle axe", WEAPON_TYPE_BATTLE_AXE);
        cvariant(CRAFT_RECIPE_WEAPON_BATTLE_AXE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "battle axe");
    craft_recipe(CRAFT_RECIPE_WEAPON_FLAIL, ITEM_WEAPON, WEAPON_TYPE_FLAIL, "flail", WEAPON_TYPE_FLAIL);
        cvariant(CRAFT_RECIPE_WEAPON_FLAIL, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "flail");
    craft_recipe(CRAFT_RECIPE_WEAPON_LONG_SWORD, ITEM_WEAPON, WEAPON_TYPE_LONG_SWORD, "long sword", WEAPON_TYPE_LONG_SWORD);
        cvariant(CRAFT_RECIPE_WEAPON_LONG_SWORD, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "long sword");
        cvariant(CRAFT_RECIPE_WEAPON_LONG_SWORD, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "war sword");
        cvariant(CRAFT_RECIPE_WEAPON_LONG_SWORD, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "broad sword");
        cvariant(CRAFT_RECIPE_WEAPON_LONG_SWORD, 3, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "saber");
        cvariant(CRAFT_RECIPE_WEAPON_LONG_SWORD, 4, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "sabre");
    craft_recipe(CRAFT_RECIPE_WEAPON_HEAVY_PICK, ITEM_WEAPON, WEAPON_TYPE_HEAVY_PICK, "heavy pick", WEAPON_TYPE_HEAVY_PICK);
        cvariant(CRAFT_RECIPE_WEAPON_HEAVY_PICK, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_WOOD, 2, CRAFT_GROUP_HIDES, 1, "heavy pick");
    craft_recipe(CRAFT_RECIPE_WEAPON_RAPIER, ITEM_WEAPON, WEAPON_TYPE_RAPIER, "rapier", WEAPON_TYPE_RAPIER);
        cvariant(CRAFT_RECIPE_WEAPON_RAPIER, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "rapier");
        cvariant(CRAFT_RECIPE_WEAPON_RAPIER, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "estoc");
        cvariant(CRAFT_RECIPE_WEAPON_RAPIER, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "foil");
        cvariant(CRAFT_RECIPE_WEAPON_RAPIER, 3, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "epee");
    craft_recipe(CRAFT_RECIPE_WEAPON_SCIMITAR, ITEM_WEAPON, WEAPON_TYPE_SCIMITAR, "scimitar", WEAPON_TYPE_SCIMITAR);
        cvariant(CRAFT_RECIPE_WEAPON_SCIMITAR, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "scimitar");
        cvariant(CRAFT_RECIPE_WEAPON_SCIMITAR, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "falcata");
        cvariant(CRAFT_RECIPE_WEAPON_SCIMITAR, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "zulfiqar");
        cvariant(CRAFT_RECIPE_WEAPON_SCIMITAR, 3, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "dadao");
        cvariant(CRAFT_RECIPE_WEAPON_SCIMITAR, 4, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "kopis");
    craft_recipe(CRAFT_RECIPE_WEAPON_TRIDENT, ITEM_WEAPON, WEAPON_TYPE_TRIDENT, "trident", WEAPON_TYPE_TRIDENT);
        cvariant(CRAFT_RECIPE_WEAPON_TRIDENT, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "trident");
    craft_recipe(CRAFT_RECIPE_WEAPON_WARHAMMER, ITEM_WEAPON, WEAPON_TYPE_WARHAMMER, "warhammer", WEAPON_TYPE_WARHAMMER);
        cvariant(CRAFT_RECIPE_WEAPON_WARHAMMER, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "warhammer");
    craft_recipe(CRAFT_RECIPE_WEAPON_FALCHION, ITEM_WEAPON, WEAPON_TYPE_FALCHION, "falchion", WEAPON_TYPE_FALCHION);
        cvariant(CRAFT_RECIPE_WEAPON_FALCHION, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "falchion");
        cvariant(CRAFT_RECIPE_WEAPON_FALCHION, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "shamshir");
        cvariant(CRAFT_RECIPE_WEAPON_FALCHION, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "talwar");
    craft_recipe(CRAFT_RECIPE_WEAPON_GLAIVE, ITEM_WEAPON, WEAPON_TYPE_GLAIVE, "glaive", WEAPON_TYPE_GLAIVE);
        cvariant(CRAFT_RECIPE_WEAPON_GLAIVE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "glaive");
    craft_recipe(CRAFT_RECIPE_WEAPON_GREAT_AXE, ITEM_WEAPON, WEAPON_TYPE_GREAT_AXE, "great axe", WEAPON_TYPE_GREAT_AXE);
        cvariant(CRAFT_RECIPE_WEAPON_GREAT_AXE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "great axe");
    craft_recipe(CRAFT_RECIPE_WEAPON_GREAT_CLUB, ITEM_WEAPON, WEAPON_TYPE_GREAT_CLUB, "great club", WEAPON_TYPE_GREAT_CLUB);
        cvariant(CRAFT_RECIPE_WEAPON_GREAT_CLUB, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "great club");
    craft_recipe(CRAFT_RECIPE_WEAPON_HEAVY_FLAIL, ITEM_WEAPON, WEAPON_TYPE_HEAVY_FLAIL, "heavy flail", WEAPON_TYPE_HEAVY_FLAIL);
        cvariant(CRAFT_RECIPE_WEAPON_HEAVY_FLAIL, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "heavy flail");
    craft_recipe(CRAFT_RECIPE_WEAPON_GREAT_SWORD, ITEM_WEAPON, WEAPON_TYPE_GREAT_SWORD, "great sword", WEAPON_TYPE_GREAT_SWORD);
        cvariant(CRAFT_RECIPE_WEAPON_GREAT_SWORD, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "great sword");
        cvariant(CRAFT_RECIPE_WEAPON_GREAT_SWORD, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "claymore");
        cvariant(CRAFT_RECIPE_WEAPON_GREAT_SWORD, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "flamberge");
        cvariant(CRAFT_RECIPE_WEAPON_GREAT_SWORD, 3, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "tachi");
        cvariant(CRAFT_RECIPE_WEAPON_GREAT_SWORD, 4, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "zweihander");
    craft_recipe(CRAFT_RECIPE_WEAPON_GUISARME, ITEM_WEAPON, WEAPON_TYPE_GUISARME, "guisarme", WEAPON_TYPE_GUISARME);
        cvariant(CRAFT_RECIPE_WEAPON_GUISARME, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "guisarme");
    craft_recipe(CRAFT_RECIPE_WEAPON_HALBERD, ITEM_WEAPON, WEAPON_TYPE_HALBERD, "halberd", WEAPON_TYPE_HALBERD);
        cvariant(CRAFT_RECIPE_WEAPON_HALBERD, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "halberd");
    craft_recipe(CRAFT_RECIPE_WEAPON_LANCE, ITEM_WEAPON, WEAPON_TYPE_LANCE, "lance", WEAPON_TYPE_LANCE);
        cvariant(CRAFT_RECIPE_WEAPON_LANCE, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 6, CRAFT_GROUP_HARD_METALS, 2, 0, 0, "lance");
    craft_recipe(CRAFT_RECIPE_WEAPON_RANSEUR, ITEM_WEAPON, WEAPON_TYPE_RANSEUR, "ranseur", WEAPON_TYPE_RANSEUR);
        cvariant(CRAFT_RECIPE_WEAPON_RANSEUR, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "ranseur");
    craft_recipe(CRAFT_RECIPE_WEAPON_SCYTHE, ITEM_WEAPON, WEAPON_TYPE_SCYTHE, "scythe", WEAPON_TYPE_SCYTHE);
        cvariant(CRAFT_RECIPE_WEAPON_SCYTHE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_HIDES, 1, "scythe");
    craft_recipe(CRAFT_RECIPE_WEAPON_LONG_BOW, ITEM_WEAPON, WEAPON_TYPE_LONG_BOW, "long bow", WEAPON_TYPE_LONG_BOW);
        cvariant(CRAFT_RECIPE_WEAPON_LONG_BOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_CLOTH, 2, 0,0, "long bow");
    craft_recipe(CRAFT_RECIPE_WEAPON_SHORT_BOW, ITEM_WEAPON, WEAPON_TYPE_SHORT_BOW, "short bow", WEAPON_TYPE_SHORT_BOW);
        cvariant(CRAFT_RECIPE_WEAPON_SHORT_BOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_CLOTH, 2, 0,0, "short bow");
    craft_recipe(CRAFT_RECIPE_WEAPON_COMPOSITE_LONGBOW, ITEM_WEAPON, WEAPON_TYPE_COMPOSITE_LONGBOW_5, "composite longbow", WEAPON_TYPE_COMPOSITE_LONGBOW_5);
        cvariant(CRAFT_RECIPE_WEAPON_COMPOSITE_LONGBOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 6, CRAFT_GROUP_CLOTH, 2, 0,0, "composite long bow");
    craft_recipe(CRAFT_RECIPE_WEAPON_COMPOSITE_SHORTBOW, ITEM_WEAPON, WEAPON_TYPE_COMPOSITE_SHORTBOW_5, "composite shortbow", WEAPON_TYPE_COMPOSITE_SHORTBOW_5);
        cvariant(CRAFT_RECIPE_WEAPON_COMPOSITE_SHORTBOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_CLOTH, 2, 0,0, "composite short bow");
    craft_recipe(CRAFT_RECIPE_WEAPON_KAMA, ITEM_WEAPON, WEAPON_TYPE_KAMA, "kama", WEAPON_TYPE_KAMA);
        cvariant(CRAFT_RECIPE_WEAPON_KAMA, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_WOOD, 2, 0,0, "kama");
    craft_recipe(CRAFT_RECIPE_WEAPON_NUNCHAKU, ITEM_WEAPON, WEAPON_TYPE_NUNCHAKU, "nunchaku", WEAPON_TYPE_NUNCHAKU);
        cvariant(CRAFT_RECIPE_WEAPON_NUNCHAKU, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HARD_METALS, 2, 0,0, "nunchaku");
    craft_recipe(CRAFT_RECIPE_WEAPON_SAI, ITEM_WEAPON, WEAPON_TYPE_SAI, "sai", WEAPON_TYPE_SAI);
        cvariant(CRAFT_RECIPE_WEAPON_SAI, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0,0, "sai");
    craft_recipe(CRAFT_RECIPE_WEAPON_SIANGHAM, ITEM_WEAPON, WEAPON_TYPE_SIANGHAM, "siangham", WEAPON_TYPE_SIANGHAM);
        cvariant(CRAFT_RECIPE_WEAPON_SIANGHAM, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 2, 0,0, "siangham");
    craft_recipe(CRAFT_RECIPE_WEAPON_BASTARD_SWORD, ITEM_WEAPON, WEAPON_TYPE_BASTARD_SWORD, "bastard sword", WEAPON_TYPE_BASTARD_SWORD);
        cvariant(CRAFT_RECIPE_WEAPON_BASTARD_SWORD, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 2, 0,0, "bastard sword");
        cvariant(CRAFT_RECIPE_WEAPON_BASTARD_SWORD, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 2, 0,0, "katana");
        cvariant(CRAFT_RECIPE_WEAPON_BASTARD_SWORD, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 2, 0,0, "hand and a half sword");
    craft_recipe(CRAFT_RECIPE_WEAPON_DWARVEN_WAR_AXE, ITEM_WEAPON, WEAPON_TYPE_DWARVEN_WAR_AXE, "dwarven war axe", WEAPON_TYPE_DWARVEN_WAR_AXE);
        cvariant(CRAFT_RECIPE_WEAPON_DWARVEN_WAR_AXE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 1, 0,0, "war axe");
    craft_recipe(CRAFT_RECIPE_WEAPON_WHIP, ITEM_WEAPON, WEAPON_TYPE_WHIP, "whip", WEAPON_TYPE_WHIP);
        cvariant(CRAFT_RECIPE_WEAPON_WHIP, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_HARD_METALS, 1, 0,0, "whip");
        cvariant(CRAFT_RECIPE_WEAPON_WHIP, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_HARD_METALS, 1, 0,0, "scourge");
        cvariant(CRAFT_RECIPE_WEAPON_WHIP, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_HARD_METALS, 1, 0,0, "cat o nine tails");
    craft_recipe(CRAFT_RECIPE_WEAPON_SPIKED_CHAIN, ITEM_WEAPON, WEAPON_TYPE_SPIKED_CHAIN, "spiked chain", WEAPON_TYPE_SPIKED_CHAIN);
        cvariant(CRAFT_RECIPE_WEAPON_SPIKED_CHAIN, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, 0, 0, 0, 0, "spiked chain");
    craft_recipe(CRAFT_RECIPE_WEAPON_DOUBLE_AXE, ITEM_WEAPON, WEAPON_TYPE_DOUBLE_AXE, "double axe", WEAPON_TYPE_DOUBLE_AXE);
        cvariant(CRAFT_RECIPE_WEAPON_DOUBLE_AXE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 2, 0, 0, "double axe");
    craft_recipe(CRAFT_RECIPE_WEAPON_DIRE_FLAIL, ITEM_WEAPON, WEAPON_TYPE_DIRE_FLAIL, "dire flail", WEAPON_TYPE_DIRE_FLAIL);
        cvariant(CRAFT_RECIPE_WEAPON_DIRE_FLAIL, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 2, 0, 0, "dire flail");
    craft_recipe(CRAFT_RECIPE_WEAPON_HOOKED_HAMMER, ITEM_WEAPON, WEAPON_TYPE_HOOKED_HAMMER, "hooked hammer", WEAPON_TYPE_HOOKED_HAMMER);
        cvariant(CRAFT_RECIPE_WEAPON_HOOKED_HAMMER, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 2, 0, 0, "hooked hammer");
    craft_recipe(CRAFT_RECIPE_WEAPON_2_BLADED_SWORD, ITEM_WEAPON, WEAPON_TYPE_2_BLADED_SWORD, "two bladed_sword", WEAPON_TYPE_2_BLADED_SWORD);
        cvariant(CRAFT_RECIPE_WEAPON_2_BLADED_SWORD, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 2, 0, 0, "two bladed sword");
    craft_recipe(CRAFT_RECIPE_WEAPON_DWARVEN_URGOSH, ITEM_WEAPON, WEAPON_TYPE_DWARVEN_URGOSH, "dwarven urgosh", WEAPON_TYPE_DWARVEN_URGOSH);
        cvariant(CRAFT_RECIPE_WEAPON_DWARVEN_URGOSH, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 2, 0, 0, "urgosh");
    craft_recipe(CRAFT_RECIPE_WEAPON_HAND_CROSSBOW, ITEM_WEAPON, WEAPON_TYPE_HAND_CROSSBOW, "hand crossbow", WEAPON_TYPE_HEAVY_CROSSBOW);
        cvariant(CRAFT_RECIPE_WEAPON_HAND_CROSSBOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 3, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_HARD_METALS, 1, "hand crossbow");
    craft_recipe(CRAFT_RECIPE_WEAPON_HEAVY_REP_XBOW, ITEM_WEAPON, WEAPON_TYPE_HEAVY_REP_XBOW, "heavy repeating crossbow", WEAPON_TYPE_HEAVY_REP_XBOW);
        cvariant(CRAFT_RECIPE_WEAPON_HEAVY_REP_XBOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 6, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_HARD_METALS, 1, "heavy repeating crossbow");
    craft_recipe(CRAFT_RECIPE_WEAPON_LIGHT_REP_XBOW, ITEM_WEAPON, WEAPON_TYPE_LIGHT_REP_XBOW, "light repeating crossbow", WEAPON_TYPE_LIGHT_REP_XBOW);
        cvariant(CRAFT_RECIPE_WEAPON_LIGHT_REP_XBOW, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_HARD_METALS, 1, "light repeating crossbow");
    craft_recipe(CRAFT_RECIPE_WEAPON_BOLA, ITEM_WEAPON, WEAPON_TYPE_BOLA, "bola", WEAPON_TYPE_BOLA);
        cvariant(CRAFT_RECIPE_WEAPON_BOLA, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_HARD_METALS, 2, 0, 0, "bola");
    craft_recipe(CRAFT_RECIPE_WEAPON_NET, ITEM_WEAPON, WEAPON_TYPE_NET, "net", WEAPON_TYPE_NET);
        cvariant(CRAFT_RECIPE_WEAPON_NET, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "net");
    craft_recipe(CRAFT_RECIPE_WEAPON_SHURIKEN, ITEM_WEAPON, WEAPON_TYPE_SHURIKEN, "shuriken", WEAPON_TYPE_SHURIKEN);
        cvariant(CRAFT_RECIPE_WEAPON_SHURIKEN, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 2, 0, 0, 0, 0, "shuriken");
        cvariant(CRAFT_RECIPE_WEAPON_SHURIKEN, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 2, 0, 0, 0, 0, "ninja star");
        cvariant(CRAFT_RECIPE_WEAPON_SHURIKEN, 2, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 2, 0, 0, 0, 0, "throwing star");
    craft_recipe(CRAFT_RECIPE_WEAPON_WARMAUL, ITEM_WEAPON, WEAPON_TYPE_WARMAUL, "warmaul", WEAPON_TYPE_WARMAUL);
        cvariant(CRAFT_RECIPE_WEAPON_WARMAUL, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 8, CRAFT_GROUP_HIDES, 2, 0, 0, "war maul");
    craft_recipe(CRAFT_RECIPE_WEAPON_KHOPESH, ITEM_WEAPON, WEAPON_TYPE_KHOPESH, "khopesh", WEAPON_TYPE_KHOPESH);
        cvariant(CRAFT_RECIPE_WEAPON_KHOPESH, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "khopesh");
        cvariant(CRAFT_RECIPE_WEAPON_KHOPESH, 1, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_HIDES, 1, 0, 0, "falcata");
    craft_recipe(CRAFT_RECIPE_WEAPON_KNIFE, ITEM_WEAPON, WEAPON_TYPE_KNIFE, "knife", WEAPON_TYPE_KNIFE);
        cvariant(CRAFT_RECIPE_WEAPON_KNIFE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "knife");
    craft_recipe(CRAFT_RECIPE_WEAPON_HOOPAK, ITEM_WEAPON, WEAPON_TYPE_HOOPAK, "hoopak", WEAPON_TYPE_HOOPAK);
        cvariant(CRAFT_RECIPE_WEAPON_HOOPAK, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 3, CRAFT_GROUP_HARD_METALS, 1, "hoopak");
    craft_recipe(CRAFT_RECIPE_WEAPON_FOOTMANS_LANCE, ITEM_WEAPON, WEAPON_TYPE_FOOTMANS_LANCE, "footmans lance", WEAPON_TYPE_FOOTMANS_LANCE);
        cvariant(CRAFT_RECIPE_WEAPON_LANCE, 0, CRAFT_SKILL_WEAPONSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "footman's lance");
    craft_recipe(CRAFT_RECIPE_ARMOR_CLOTHING, ITEM_ARMOR, SPEC_ARMOR_TYPE_CLOTHING, "clothing chest piece", SPEC_ARMOR_TYPE_CLOTHING);
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "robe");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "shirt");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "tunic");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "gown");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "dress");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "coat");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "jacket");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING, 7, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "raiment");
    craft_recipe(CRAFT_RECIPE_ARMOR_PADDED, ITEM_ARMOR, SPEC_ARMOR_TYPE_PADDED, "padded chest piece", SPEC_ARMOR_TYPE_PADDED);
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "padded robe");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "padded shirt");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "padded tunic");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "padded gown");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "padded dress");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "padded coat");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "padded jacket");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 7, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "gambeson");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED, 8, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 7, CRAFT_GROUP_HIDES, 1, 0, 0, "padded raiment");
    craft_recipe(CRAFT_RECIPE_ARMOR_LEATHER, ITEM_ARMOR, SPEC_ARMOR_TYPE_LEATHER, "leather chest piece", SPEC_ARMOR_TYPE_LEATHER);
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather hauberk");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather coat");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather jacket");
    craft_recipe(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER, ITEM_ARMOR, SPEC_ARMOR_TYPE_STUDDED_LEATHER, "studded leather chest piece", SPEC_ARMOR_TYPE_STUDDED_LEATHER);
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "studded leather breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "studded leather hauberk");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "studded leather coat");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "studded leather jacket");
    craft_recipe(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN, ITEM_ARMOR, SPEC_ARMOR_TYPE_LIGHT_CHAIN, "light chainmail chest piece", SPEC_ARMOR_TYPE_LIGHT_CHAIN);
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, "chain shirt");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, "chainmail shirt");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, "chain gown");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, "chainmail gown");
    craft_recipe(CRAFT_RECIPE_ARMOR_HIDE, ITEM_ARMOR, SPEC_ARMOR_TYPE_HIDE, "hide chest piece", SPEC_ARMOR_TYPE_HIDE);
        cvariant(CRAFT_RECIPE_ARMOR_HIDE, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide coat");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 7, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide jacket");
    craft_recipe(CRAFT_RECIPE_ARMOR_SCALE, ITEM_ARMOR, SPEC_ARMOR_TYPE_SCALE, "scale chest pice", SPEC_ARMOR_TYPE_SCALE);
        cvariant(CRAFT_RECIPE_ARMOR_SCALE, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 4, 0, 0, "scale mail hauberk");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 4, 0, 0, "scale hauberk");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 4, 0, 0, "scale mail coat");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 4, 0, 0, "scale coat");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE, 4, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 4, 0, 0, "scale mail cuirass");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE, 5, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 4, 0, 0, "scale cuirass");
    craft_recipe(CRAFT_RECIPE_ARMOR_CHAINMAIL, ITEM_ARMOR, SPEC_ARMOR_TYPE_CHAINMAIL, "chainmail chest piece", SPEC_ARMOR_TYPE_CHAINMAIL);
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 4, 0, 0, "chain mail hauberk");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 4, 0, 0, "chain mail coat");
    craft_recipe(CRAFT_RECIPE_ARMOR_PIECEMEAL, ITEM_ARMOR, SPEC_ARMOR_TYPE_PIECEMEAL, "piecemeal chest piece", SPEC_ARMOR_TYPE_PIECEMEAL);
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 2, CRAFT_GROUP_HIDES, 3, "breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 2, CRAFT_GROUP_HIDES, 3, "cuirass");
    craft_recipe(CRAFT_RECIPE_ARMOR_SPLINT, ITEM_ARMOR, SPEC_ARMOR_TYPE_SPLINT, "splint mail chest piece", SPEC_ARMOR_TYPE_SPLINT);
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "splint mail breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "splint breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "splint mail cuirass");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "splint cuirass");
    craft_recipe(CRAFT_RECIPE_ARMOR_BANDED, ITEM_ARMOR, SPEC_ARMOR_TYPE_BANDED, "banded mail chest piece", SPEC_ARMOR_TYPE_BANDED);
        cvariant(CRAFT_RECIPE_ARMOR_BANDED, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "banded breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "banded mail breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "banded cuirass");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "banded mail cuirass");
    craft_recipe(CRAFT_RECIPE_ARMOR_HALF_PLATE, ITEM_ARMOR, SPEC_ARMOR_TYPE_HALF_PLATE, "half plate chest piece", SPEC_ARMOR_TYPE_HALF_PLATE);
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "half plate breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "half plate cuirass");
    craft_recipe(CRAFT_RECIPE_ARMOR_FULL_PLATE, ITEM_ARMOR, SPEC_ARMOR_TYPE_FULL_PLATE, "full plate chest piece ", SPEC_ARMOR_TYPE_FULL_PLATE);
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "full plate breastplate");
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 7, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 3, "full plate cuirass");
    craft_recipe(CRAFT_RECIPE_ARMOR_BUCKLER, ITEM_ARMOR, SPEC_ARMOR_TYPE_BUCKLER, "buckler", SPEC_ARMOR_TYPE_BUCKLER);
        cvariant(CRAFT_RECIPE_ARMOR_BUCKLER, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_WOOD, 1, CRAFT_GROUP_HIDES, 1, "buckler");
    craft_recipe(CRAFT_RECIPE_ARMOR_SMALL_SHIELD, ITEM_ARMOR, SPEC_ARMOR_TYPE_SMALL_SHIELD, "small shield", SPEC_ARMOR_TYPE_SMALL_SHIELD);
        cvariant(CRAFT_RECIPE_ARMOR_SMALL_SHIELD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "light shield");
        cvariant(CRAFT_RECIPE_ARMOR_SMALL_SHIELD, 1, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "small shield");
    craft_recipe(CRAFT_RECIPE_ARMOR_LARGE_SHIELD, ITEM_ARMOR, SPEC_ARMOR_TYPE_LARGE_SHIELD, "large shield", SPEC_ARMOR_TYPE_LARGE_SHIELD);
        cvariant(CRAFT_RECIPE_ARMOR_LARGE_SHIELD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "heavy shield");
        cvariant(CRAFT_RECIPE_ARMOR_LARGE_SHIELD, 1, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "large shield");
    craft_recipe(CRAFT_RECIPE_ARMOR_TOWER_SHIELD, ITEM_ARMOR, SPEC_ARMOR_TYPE_TOWER_SHIELD, "tower shield", SPEC_ARMOR_TYPE_TOWER_SHIELD);
        cvariant(CRAFT_RECIPE_ARMOR_TOWER_SHIELD, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "tower shield");
        cvariant(CRAFT_RECIPE_ARMOR_TOWER_SHIELD, 1, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "long shield");
        cvariant(CRAFT_RECIPE_ARMOR_TOWER_SHIELD, 2, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 8, CRAFT_GROUP_HIDES, 1, 0, 0, "body shield");
    craft_recipe(CRAFT_RECIPE_ARMOR_CLOTHING_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_CLOTHING_HEAD, "clothing helm", SPEC_ARMOR_TYPE_CLOTHING_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_HEAD, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "hat");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_HEAD, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "cap");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_HEAD, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "hood");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_HEAD, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "cowl");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_HEAD, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "fedora");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_HEAD, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "turban");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_HEAD, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "tricorn");
    craft_recipe(CRAFT_RECIPE_ARMOR_PADDED_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_PADDED_HEAD, "padded helm", SPEC_ARMOR_TYPE_PADDED_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_HEAD, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "padded helm");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_HEAD, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "padded cap");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_HEAD, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "padded headgear");
    craft_recipe(CRAFT_RECIPE_ARMOR_LEATHER_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_LEATHER_HEAD, "leather helm", SPEC_ARMOR_TYPE_LEATHER_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_HEAD, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather helm");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_HEAD, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather cap");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_HEAD, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather headgear");
    craft_recipe(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_STUDDED_LEATHER_HEAD, "studded leather helm", SPEC_ARMOR_TYPE_STUDDED_LEATHER_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_HEAD, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather helm");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_HEAD, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather cap");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_HEAD, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather headgear");
    craft_recipe(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_LIGHT_CHAIN_HEAD, "light chain helm", SPEC_ARMOR_TYPE_LIGHT_CHAIN_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_HEAD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail coif");
    craft_recipe(CRAFT_RECIPE_ARMOR_HIDE_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_HIDE_HEAD, "hide helm", SPEC_ARMOR_TYPE_HIDE_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_HEAD, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide helm");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_HEAD, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide cap");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_HEAD, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide headgear");
    craft_recipe(CRAFT_RECIPE_ARMOR_SCALE_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_SCALE_HEAD, "scale helm", SPEC_ARMOR_TYPE_SCALE_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_HEAD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale coif");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_HEAD, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scalemail coif");
    craft_recipe(CRAFT_RECIPE_ARMOR_CHAINMAIL_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_CHAINMAIL_HEAD, "chainmail helm", SPEC_ARMOR_TYPE_CHAINMAIL_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_HEAD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chain coif");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_HEAD, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail coif");
    craft_recipe(CRAFT_RECIPE_ARMOR_PIECEMEAL_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_PIECEMEAL_HEAD, "piecemeal helm", SPEC_ARMOR_TYPE_PIECEMEAL_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL_HEAD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "helm");
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL_HEAD, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "headgear");
    craft_recipe(CRAFT_RECIPE_ARMOR_SPLINT_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_SPLINT_HEAD, "splint helm", SPEC_ARMOR_TYPE_SPLINT_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_HEAD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splint helm");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_HEAD, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splint headgear");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_HEAD, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splintmail helm");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_HEAD, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splintmail headgear");
    craft_recipe(CRAFT_RECIPE_ARMOR_BANDED_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_BANDED_HEAD, "banded helm", SPEC_ARMOR_TYPE_BANDED_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_HEAD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded helm");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_HEAD, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded headgear");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_HEAD, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded mail helm");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_HEAD, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded mail headgear");
    craft_recipe(CRAFT_RECIPE_ARMOR_HALF_PLATE_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_HALF_PLATE_HEAD, "half plate helm", SPEC_ARMOR_TYPE_HALF_PLATE_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE_HEAD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "half plate helm");
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE_HEAD, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "half plate greathelm");
    craft_recipe(CRAFT_RECIPE_ARMOR_FULL_PLATE_HEAD, ITEM_ARMOR, SPEC_ARMOR_TYPE_FULL_PLATE_HEAD, "full plate helm", SPEC_ARMOR_TYPE_FULL_PLATE_HEAD);
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE_HEAD, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "full plate helm");
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE_HEAD, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "full plate greathelm");
    craft_recipe(CRAFT_RECIPE_ARMOR_CLOTHING_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_CLOTHING_ARMS, "clothing arms", SPEC_ARMOR_TYPE_CLOTHING_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_ARMS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "sleeves");
    craft_recipe(CRAFT_RECIPE_ARMOR_PADDED_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_PADDED_ARMS, "padded arms", SPEC_ARMOR_TYPE_PADDED_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_ARMS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "padded sleeves");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_ARMS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "padded armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_LEATHER_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_LEATHER_ARMS, "leather arms", SPEC_ARMOR_TYPE_LEATHER_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_ARMS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather sleeves");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_ARMS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_ARMS, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_STUDDED_LEATHER_ARMS, "studded leather arms", SPEC_ARMOR_TYPE_STUDDED_LEATHER_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_ARMS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather sleeves");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_ARMS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_ARMS, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_LIGHT_CHAIN_ARMS, "light chain arms", SPEC_ARMOR_TYPE_LIGHT_CHAIN_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_ARMS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail sleeves");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_ARMS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_HIDE_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_HIDE_ARMS, "hide arms", SPEC_ARMOR_TYPE_HIDE_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_ARMS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide sleeves");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_ARMS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_ARMS, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_SCALE_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_SCALE_ARMS, "scale arms", SPEC_ARMOR_TYPE_SCALE_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_ARMS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_ARMS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scalemail vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_ARMS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale armguards");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_ARMS, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scalemail armguards");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_ARMS, 4, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale sleeves");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_ARMS, 5, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 4, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scalemail sleeves");
    craft_recipe(CRAFT_RECIPE_ARMOR_CHAINMAIL_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_CHAINMAIL_ARMS, "chainmail arms", SPEC_ARMOR_TYPE_CHAINMAIL_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_ARMS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail sleeves");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_ARMS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail armguards");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_ARMS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chain sleeves");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_ARMS, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chain armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_PIECEMEAL_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_PIECEMEAL_ARMS, "piecemeal arms", SPEC_ARMOR_TYPE_PIECEMEAL_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL_ARMS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL_ARMS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_SPLINT_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_SPLINT_ARMS, "splint arms", SPEC_ARMOR_TYPE_SPLINT_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_ARMS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splint vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_ARMS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splintmail vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_ARMS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splint armguards");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_ARMS, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splintmail armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_BANDED_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_BANDED_ARMS, "banded arms", SPEC_ARMOR_TYPE_BANDED_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_ARMS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_ARMS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "bandedmail vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_ARMS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded armguards");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_ARMS, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "bandedmail armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_HALF_PLATE_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_HALF_PLATE_ARMS, "half plate arms", SPEC_ARMOR_TYPE_HALF_PLATE_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE_ARMS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "half plate vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE_ARMS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "half plate armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_FULL_PLATE_ARMS, ITEM_ARMOR, SPEC_ARMOR_TYPE_FULL_PLATE_ARMS, "full plate arms", SPEC_ARMOR_TYPE_FULL_PLATE_ARMS);
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE_ARMS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "full plate vambraces");
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE_ARMS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 6, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "full plate armguards");
    craft_recipe(CRAFT_RECIPE_ARMOR_CLOTHING_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_CLOTHING_LEGS, "clothing legs", SPEC_ARMOR_TYPE_CLOTHING_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_LEGS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "pants");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_LEGS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "skirt");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_LEGS, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "kilt");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_LEGS, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "trousers");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_LEGS, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "leggings");
        cvariant(CRAFT_RECIPE_ARMOR_CLOTHING_LEGS, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, 0, 0, "slacks");
    craft_recipe(CRAFT_RECIPE_ARMOR_PADDED_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_PADDED_LEGS, "padded legs", SPEC_ARMOR_TYPE_PADDED_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_LEGS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "padded pants");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_LEGS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "padded skirt");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_LEGS, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "padded kilt");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_LEGS, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "padded trousers");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_LEGS, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "padded leggings");
        cvariant(CRAFT_RECIPE_ARMOR_PADDED_LEGS, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, CRAFT_GROUP_HIDES, 1, 0, 0, "padded slacks");
    craft_recipe(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_LEATHER_LEGS, "leather legs", SPEC_ARMOR_TYPE_LEATHER_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather pants");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather skirt");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather kilt");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather trousers");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather leggings");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather slacks");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather culottes");
        cvariant(CRAFT_RECIPE_ARMOR_LEATHER_LEGS, 7, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather greaves");
    craft_recipe(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_STUDDED_LEATHER_LEGS, "studded leather legs", SPEC_ARMOR_TYPE_STUDDED_LEATHER_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather pants");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather skirt");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather kilt");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather trousers");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather leggings");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather slacks");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather culottes");
        cvariant(CRAFT_RECIPE_ARMOR_STUDDED_LEATHER_LEGS, 7, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HARD_METALS, 1, "studded leather greaves");
    craft_recipe(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_LIGHT_CHAIN_LEGS, "light chain legs", SPEC_ARMOR_TYPE_LIGHT_CHAIN_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_LEGS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail pants");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_LEGS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail skirt");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_LEGS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail kilt");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_LEGS, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail trousers");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_LEGS, 4, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail leggings");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_LEGS, 5, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail slacks");
        cvariant(CRAFT_RECIPE_ARMOR_LIGHT_CHAIN_LEGS, 6, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "light chainmail culottes");
    craft_recipe(CRAFT_RECIPE_ARMOR_HIDE_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_HIDE_LEGS, "hide legs", SPEC_ARMOR_TYPE_HIDE_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_LEGS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide pants");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_LEGS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide skirt");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_LEGS, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide kilt");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_LEGS, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide trousers");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_LEGS, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide leggings");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_LEGS, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide slacks");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_LEGS, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide culottes");
        cvariant(CRAFT_RECIPE_ARMOR_HIDE_LEGS, 7, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 5, CRAFT_GROUP_CLOTH, 1, 0, 0, "hide greaves");
    craft_recipe(CRAFT_RECIPE_ARMOR_SCALE_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_SCALE_LEGS, "scale legs", SPEC_ARMOR_TYPE_SCALE_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_LEGS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale pants");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_LEGS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale skirt");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_LEGS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale kilt");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_LEGS, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale trousers");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_LEGS, 4, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale leggings");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_LEGS, 5, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale slacks");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_LEGS, 6, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale culottes");
        cvariant(CRAFT_RECIPE_ARMOR_SCALE_LEGS, 7, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "scale greaves");
    craft_recipe(CRAFT_RECIPE_ARMOR_CHAINMAIL_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_CHAINMAIL_LEGS, "chainmail legs", SPEC_ARMOR_TYPE_CHAINMAIL_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_LEGS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail pants");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_LEGS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail skirt");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_LEGS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail kilt");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_LEGS, 3, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail trousers");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_LEGS, 4, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail leggings");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_LEGS, 5, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail slacks");
        cvariant(CRAFT_RECIPE_ARMOR_CHAINMAIL_LEGS, 6, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "chainmail culottes");
    craft_recipe(CRAFT_RECIPE_ARMOR_PIECEMEAL_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_PIECEMEAL_LEGS, "piecemeal legs", SPEC_ARMOR_TYPE_PIECEMEAL_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL_LEGS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "greaves");
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL_LEGS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "leg guards");
        cvariant(CRAFT_RECIPE_ARMOR_PIECEMEAL_LEGS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "jambeau");
    craft_recipe(CRAFT_RECIPE_ARMOR_SPLINT_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_SPLINT_LEGS, "splint legs", SPEC_ARMOR_TYPE_SPLINT_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_LEGS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splint greaves");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_LEGS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splint leg guards");
        cvariant(CRAFT_RECIPE_ARMOR_SPLINT_LEGS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "splint jambeau");
    craft_recipe(CRAFT_RECIPE_ARMOR_BANDED_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_BANDED_LEGS, "banded legs", SPEC_ARMOR_TYPE_BANDED_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_LEGS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded greaves");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_LEGS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded leg guards");
        cvariant(CRAFT_RECIPE_ARMOR_BANDED_LEGS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "banded jambeau");
    craft_recipe(CRAFT_RECIPE_ARMOR_HALF_PLATE_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_HALF_PLATE_LEGS, "half plate legs", SPEC_ARMOR_TYPE_HALF_PLATE_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE_LEGS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "half plate greaves");
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE_LEGS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "half plate leg guards");
        cvariant(CRAFT_RECIPE_ARMOR_HALF_PLATE_LEGS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "half plate jambeau");
    craft_recipe(CRAFT_RECIPE_ARMOR_FULL_PLATE_LEGS, ITEM_ARMOR, SPEC_ARMOR_TYPE_FULL_PLATE_LEGS, "full plate legs", SPEC_ARMOR_TYPE_FULL_PLATE_LEGS);
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE_LEGS, 0, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "full plate greaves");
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE_LEGS, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "full plate leg guards");
        cvariant(CRAFT_RECIPE_ARMOR_FULL_PLATE_LEGS, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 5, CRAFT_GROUP_CLOTH, 1, CRAFT_GROUP_HIDES, 1, "full plate jambeau");
    craft_recipe(CRAFT_RECIPE_MISC_RING, ITEM_WORN, ITEM_WEAR_FINGER, "ring", CRAFT_JEWELRY_RING);
        cvariant(CRAFT_RECIPE_MISC_RING, 0, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 1, 0, 0, 0, 0, "ring");
        cvariant(CRAFT_RECIPE_MISC_RING, 1, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 1, 0, 0, 0, 0, "band");
    craft_recipe(CRAFT_RECIPE_MISC_NECKLACE, ITEM_WORN, ITEM_WEAR_NECK, "necklace", CRAFT_JEWELRY_NECKLACE);
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 0, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "necklace");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 1, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "chain");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 2, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "pendant");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 3, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "amulet");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 4, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 2, 0, 0, 0, 0, "gorget");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, 0, 0, 0, 0, "gorget");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, 0, 0, 0, 0, "choker");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 7, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "choker");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 8, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 2, 0, 0, 0, 0, "string");
        cvariant(CRAFT_RECIPE_MISC_NECKLACE, 9, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, 0, 0, 0, 0, "string");
    craft_recipe(CRAFT_RECIPE_MISC_BOOTS, ITEM_WORN, ITEM_WEAR_FEET, "boots", CRAFT_MISC_BOOTS);
        cvariant(CRAFT_RECIPE_MISC_BOOTS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 3, 0, 0, 0, 0, "boots");
        cvariant(CRAFT_RECIPE_MISC_BOOTS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 3, 0, 0, 0, 0, "shoes");
        cvariant(CRAFT_RECIPE_MISC_BOOTS, 2, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "clogs");
        cvariant(CRAFT_RECIPE_MISC_BOOTS, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 3, 0, 0, 0, 0, "moccasins");
        cvariant(CRAFT_RECIPE_MISC_BOOTS, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, 0, 0, 0, 0, "slippers");
        cvariant(CRAFT_RECIPE_MISC_BOOTS, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "sollerets");
        cvariant(CRAFT_RECIPE_MISC_BOOTS, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "sabatons");
    craft_recipe(CRAFT_RECIPE_MISC_GLOVES, ITEM_WORN, ITEM_WEAR_HANDS, "gloves", CRAFT_MISC_GLOVES);
        cvariant(CRAFT_RECIPE_MISC_GLOVES, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 3, CRAFT_GROUP_CLOTH, 1, 0, 0, "leather gloves");
        cvariant(CRAFT_RECIPE_MISC_GLOVES, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, 0, 0, 0, 0, "gloves");
        cvariant(CRAFT_RECIPE_MISC_GLOVES, 2, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_HIDES, 1, 0, 0, "gauntlets");
        cvariant(CRAFT_RECIPE_MISC_GLOVES, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 4, 0, 0, 0, 0, "leather gauntlets");
        cvariant(CRAFT_RECIPE_MISC_GLOVES, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, 0, 0, 0, 0, "mitts");
        cvariant(CRAFT_RECIPE_MISC_GLOVES, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HARD_METALS, 3, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_CLOTH, 1, "leather mitts");
        cvariant(CRAFT_RECIPE_MISC_GLOVES, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, 0, 0, 0, 0, "mittens");
    craft_recipe(CRAFT_RECIPE_MISC_CLOAK, ITEM_WORN, ITEM_WEAR_ABOUT, "cloak", CRAFT_MISC_CLOAK);
        cvariant(CRAFT_RECIPE_MISC_CLOAK, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_SOFT_METALS, 1, "cloak");
        cvariant(CRAFT_RECIPE_MISC_CLOAK, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_SOFT_METALS, 1, "cape");
        cvariant(CRAFT_RECIPE_MISC_CLOAK, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, 0, 0, 0, 0, "shawl");
        cvariant(CRAFT_RECIPE_MISC_CLOAK, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, 0, 0, 0, 0, "stole");
        cvariant(CRAFT_RECIPE_MISC_CLOAK, 4, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_SOFT_METALS, 1, "poncho");
        cvariant(CRAFT_RECIPE_MISC_CLOAK, 5, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, 0, 0, 0, 0, "wrap");
        cvariant(CRAFT_RECIPE_MISC_CLOAK, 6, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 5, 0, 0, 0, 0, "shroud");
        cvariant(CRAFT_RECIPE_MISC_CLOAK, 7, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 4, CRAFT_GROUP_HIDES, 1, CRAFT_GROUP_SOFT_METALS, 1, "mantle");
    craft_recipe(CRAFT_RECIPE_MISC_BELT, ITEM_WORN, ITEM_WEAR_WAIST, "belt", CRAFT_MISC_BELT);
        cvariant(CRAFT_RECIPE_MISC_BELT, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "belt");
        cvariant(CRAFT_RECIPE_MISC_BELT, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "girdle");
        cvariant(CRAFT_RECIPE_MISC_BELT, 2, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 2, 0, 0, 0, 0, "sash");
        cvariant(CRAFT_RECIPE_MISC_BELT, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 2, 0, 0, 0, 0, "cord");
    craft_recipe(CRAFT_RECIPE_MISC_PAULDRONS, ITEM_WORN, ITEM_WEAR_SHOULDERS, "pauldron", CRAFT_MISC_SHOULDERS);
        cvariant(CRAFT_RECIPE_MISC_PAULDRONS, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_CLOTH, 3, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "epaulets");
        cvariant(CRAFT_RECIPE_MISC_PAULDRONS, 1, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HARD_METALS, 4, 0, 0, 0, 0, "pauldron");
    craft_recipe(CRAFT_RECIPE_MISC_ANKLET, ITEM_WORN, ITEM_WEAR_ANKLE, "anklet", CRAFT_MISC_ANKLET);
        cvariant(CRAFT_RECIPE_MISC_ANKLET, 0, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 1, 0, 0, 0, 0, "anklet");
    craft_recipe(CRAFT_RECIPE_MISC_BRACER, ITEM_WORN, ITEM_WEAR_WRIST, "bracer", CRAFT_JEWELRY_BRACELET);
        cvariant(CRAFT_RECIPE_MISC_BRACER, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "bracer");
        cvariant(CRAFT_RECIPE_MISC_BRACER, 1, CRAFT_SKILL_ARMORSMITH, CRAFT_GROUP_HARD_METALS, 2, CRAFT_GROUP_HIDES, 1, 0, 0, "bracer");
        cvariant(CRAFT_RECIPE_MISC_BRACER, 2, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "bracelet");
        cvariant(CRAFT_RECIPE_MISC_BRACER, 3, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_CLOTH, 1, 0, 0, "armband");
        cvariant(CRAFT_RECIPE_MISC_BRACER, 4, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "armlet");
        cvariant(CRAFT_RECIPE_MISC_BRACER, 5, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "bracelet");
    craft_recipe(CRAFT_RECIPE_MISC_MASK, ITEM_WORN, ITEM_WEAR_FACE, "mask", CRAFT_MISC_MASK);
        cvariant(CRAFT_RECIPE_MISC_MASK, 0, CRAFT_SKILL_TAILOR, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_CLOTH, 1, 0, 0, "mask");
        cvariant(CRAFT_RECIPE_MISC_MASK, 1, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, CRAFT_GROUP_SOFT_METALS, 1, 0, 0, "mask");
        cvariant(CRAFT_RECIPE_MISC_MASK, 2, CRAFT_SKILL_JEWELER, CRAFT_GROUP_HARD_METALS, 2, CRAFT_GROUP_HIDES, 1, 0, 0, "face plate");
        cvariant(CRAFT_RECIPE_MISC_MASK, 3, CRAFT_SKILL_JEWELER, CRAFT_GROUP_HARD_METALS, 2, CRAFT_GROUP_HIDES, 1, 0, 0, "visor");
    craft_recipe(CRAFT_RECIPE_MISC_EARRING, ITEM_WORN, ITEM_WEAR_EAR, "earrings", CRAFT_JEWELRY_EARRING);
        cvariant(CRAFT_RECIPE_MISC_EARRING, 0, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "earring");
        cvariant(CRAFT_RECIPE_MISC_EARRING, 1, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "stud");
        cvariant(CRAFT_RECIPE_MISC_EARRING, 2, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "hoop");
        cvariant(CRAFT_RECIPE_MISC_EARRING, 3, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "loop");
    craft_recipe(CRAFT_RECIPE_MISC_GLASSES, ITEM_WORN, ITEM_WEAR_EYES, "glasses", CRAFT_JEWELRY_GLASSES);
        cvariant(CRAFT_RECIPE_MISC_GLASSES, 0, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "glasses");
        cvariant(CRAFT_RECIPE_MISC_GLASSES, 1, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "spectacles");
        cvariant(CRAFT_RECIPE_MISC_GLASSES, 2, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "monocle");
        cvariant(CRAFT_RECIPE_MISC_GLASSES, 3, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "goggles");
    craft_recipe(CRAFT_RECIPE_INSTRUMENT_LYRE, ITEM_INSTRUMENT, INSTRUMENT_LYRE, "lyre", CRAFT_INSTRUMENT_LYRE);
        cvariant(CRAFT_RECIPE_INSTRUMENT_LYRE, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "lyre");
    craft_recipe(CRAFT_RECIPE_INSTRUMENT_FLUTE, ITEM_INSTRUMENT, INSTRUMENT_FLUTE, "flute", CRAFT_INSTRUMENT_FLUTE);
        cvariant(CRAFT_RECIPE_INSTRUMENT_FLUTE, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 2, 0, 0, 0, 0, "flute");
        cvariant(CRAFT_RECIPE_INSTRUMENT_FLUTE, 0, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "flute");
        cvariant(CRAFT_RECIPE_INSTRUMENT_FLUTE, 1, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "recorder");
        cvariant(CRAFT_RECIPE_INSTRUMENT_FLUTE, 2, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "clarinet");
        cvariant(CRAFT_RECIPE_INSTRUMENT_FLUTE, 3, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "pipes");
        cvariant(CRAFT_RECIPE_INSTRUMENT_FLUTE, 4, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 2, 0, 0, 0, 0, "piccolo");
    craft_recipe(CRAFT_RECIPE_INSTRUMENT_HORN, ITEM_INSTRUMENT, INSTRUMENT_HORN, "horn", CRAFT_INSTRUMENT_HORN);
        cvariant(CRAFT_RECIPE_INSTRUMENT_HORN, 0, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 4, 0, 0, 0, 0, "horn");
        cvariant(CRAFT_RECIPE_INSTRUMENT_HORN, 1, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 4, 0, 0, 0, 0, "bassoon");
        cvariant(CRAFT_RECIPE_INSTRUMENT_HORN, 2, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 4, 0, 0, 0, 0, "trombone");
        cvariant(CRAFT_RECIPE_INSTRUMENT_HORN, 3, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 4, 0, 0, 0, 0, "oboe");
        cvariant(CRAFT_RECIPE_INSTRUMENT_HORN, 4, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 4, 0, 0, 0, 0, "tuba");
        cvariant(CRAFT_RECIPE_INSTRUMENT_HORN, 5, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 4, 0, 0, 0, 0, "saxophone");
    craft_recipe(CRAFT_RECIPE_INSTRUMENT_DRUM, ITEM_INSTRUMENT, INSTRUMENT_DRUM, "drum", CRAFT_INSTRUMENT_DRUM);
        cvariant(CRAFT_RECIPE_INSTRUMENT_DRUM, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_HARD_METALS, 1, "drum");
        cvariant(CRAFT_RECIPE_INSTRUMENT_DRUM, 1, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HIDES, 2, CRAFT_GROUP_HARD_METALS, 1, "bongo");
    craft_recipe(CRAFT_RECIPE_INSTRUMENT_HARP, ITEM_INSTRUMENT, INSTRUMENT_HARP, "harp", CRAFT_INSTRUMENT_HARP);
        cvariant(CRAFT_RECIPE_INSTRUMENT_HARP, 0, CRAFT_SKILL_JEWELER, CRAFT_GROUP_SOFT_METALS, 4, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "harp");
    craft_recipe(CRAFT_RECIPE_INSTRUMENT_MANDOLIN, ITEM_INSTRUMENT, INSTRUMENT_MANDOLIN, "mandolin", CRAFT_INSTRUMENT_MANDOLIN);
        cvariant(CRAFT_RECIPE_INSTRUMENT_MANDOLIN, 0, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "mandolin");
        cvariant(CRAFT_RECIPE_INSTRUMENT_MANDOLIN, 1, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "lute");
        cvariant(CRAFT_RECIPE_INSTRUMENT_MANDOLIN, 2, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "ukulele");
        cvariant(CRAFT_RECIPE_INSTRUMENT_MANDOLIN, 3, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "guitar");
        cvariant(CRAFT_RECIPE_INSTRUMENT_MANDOLIN, 4, CRAFT_SKILL_CARPENTER, CRAFT_GROUP_WOOD, 4, CRAFT_GROUP_HARD_METALS, 1, 0, 0, "banjo");
        
}