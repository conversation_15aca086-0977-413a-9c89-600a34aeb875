# Luminari Source - Complete File Map

This document contains a complete map of all files in the Luminari Source project.

## Root Directory Files

.clang-format
.gitignore
_config.yml
account.c
account.h
act.comm.c
act.comm.do_spec_comm.c
act.h
act.informative.c
act.item.c
act.movement.c
act.offensive.c
act.other.c
act.social.c
act.wizard.c
actionqueues.c
actionqueues.h
actions.c
actions.h
aedit.c
alchemy.c
alchemy.h
asciimap.c
asciimap.h
assign_wpn_armor.c
assign_wpn_armor.h
backgrounds.c
backgrounds.h
ban.c
ban.h
bardic_performance.c
bardic_performance.h
boards.c
boards.h
bonus_breakdown.php
bonuses.php
bool.h
bsd-snprintf.c
bsd-snprintf.h
cedit.c
char_descs.c
char_descs.h
clan.c
clan.h
clan_edit.c
class.c
class.h
CLAUDE.md
CODE_OF_CONDUCT.md
combat_modes.c
combat_modes.h
comm.c
comm.h
conf.h
conf.h.in
config.c
config.h
constants.c
constants.h
CONTRIBUTING.md
copydlbinarytodev.sh
copydlbinarytolive.sh
copyfrbinarytodev.sh
copyfrbinarytolive.sh
copylumbinarytodev.sh
copylumbinarytolive.sh
craft.c
craft.h
crafting_new.c
crafting_new.h
crafting_recipes.c
crafting_recipes.h
crafting-notes.txt
crafts.c
crafts.h
db.c
db.h
deities.c
deities.h
depend
depend.4
depend.bak
desc_engine.c
desc_engine.h
dg_comm.c
dg_db_scripts.c
dg_event.c
dg_event.h
dg_handler.c
dg_misc.c
dg_mobcmd.c
dg_objcmd.c
dg_olc.c
dg_olc.h
dg_scripts.c
dg_scripts.h
dg_triggers.c
dg_variables.c
dg_wldcmd.c
domain_powers.c
domains_schools.c
domains_schools.h
dox_noGraphs.doxyfile
dox_withGraphs.doxyfile
encounters.c
encounters.h
enter_encounter.php
enter_hunt.php
enter_spell_help.php
evolutions.c
evolutions.h
feats.c
feats.h
fight.c
fight.h
gain.c
genmob.c
genmob.h
genobj.c
genobj.h
genolc.c
genolc.h
genqst.c
genshp.c
genshp.h
genwld.c
genwld.h
genzon.c
genzon.h
graph.c
graph.h
grapple.c
grapple.h
handler.c
handler.h
hedit.c
hedit.h
help.c
help.h
helpers.c
helpers.h
hlqedit.c
hlquest.c
hlquest.h
house.c
house.h
hsedit.c
htmlh-head
htmlh-tail
hunts.c
hunts.h
ibt.c
ibt.h
improved-edit.c
improved-edit.h
interpreter.c
interpreter.h
item.h
kdtree.c
kdtree.h
LICENSE
limits.c
lists.c
lists.h
magic.c
mail.c
mail.h
Makefile
Makefile.2
Makefile.3
Makefile.4
Makefile.in
Makefile2
medit.c
missions.c
missions.h
mobact.c
mobact.h
modify.c
modify.h
msgedit.c
msgedit.h
mud_event.c
mud_event.h
mud_options.example.h
mudlim.h
mysql.c
mysql.h
new_mail.c
new_mail.h
newdepend.sh
oasis.c
oasis.h
oasis_copy.c
oasis_delete.c
oasis_list.c
objsave.c
oedit.c
perfmon.cpp
perfmon.h
perlin.c
perlin.h
pfdefaults.h
players.c
prefedit.c
prefedit.h
premadebuilds.c
premadebuilds.h
protocol.c
protocol.h
psionics.c
psionics.h
qedit.c
quest.c
quest.h
race.c
race.h
random.c
random_names.c
random_names.h
rank.c
README.md
redit.c
roleplay.c
roleplay.h
SCOPTIONS
screen.h
sedit.c
shop.c
shop.h
spec_abilities.c
spec_abilities.h
spec_assign.c
spec_procs.c
spec_procs.h
specs.artifacts.c
specs.artifacts.h
spell_parser.c
spell_prep.c
spell_prep.h
spellbook_scroll.c
spells.c
spells.h
staff_events.c
staff_events.h
structs.h
study.c
sysdep.h
tedit.c
telnet.h
templates.c
templates.h
trade.c
trade.h
trails.h
transport.c
transport.h
traps.c
traps.h
treasure.c
treasure.h
treasure_const.c
utils.c
utils.h
vnums.example.h
weather.c
wilderness.c
wilderness.h
zedit.c
zmalloc.c
zmalloc.h
zone_procs.c

## .claude Directory

.claude\settings.local.json

## .github Directory

.github\auto_assign.yml
.github\ISSUE_TEMPLATE\bug_report.md
.github\ISSUE_TEMPLATE\feature_request.md

## .vscode Directory

.vscode\c_cpp_properties.json

## documentation Directory

documentation\(In Progress) Current Gear Stats Distribution.xlsx
documentation\ARCHITECTURE.md
documentation\Attaching to Wilderness.docx
documentation\Luminari - Armor Notes.docx
documentation\LuminariMUD_AI_FRIENDLY_PACK.xml
documentation\Points Standards.xlsx
documentation\Stat Distribution on Gear Drops.xlsx
documentation\Stats_by_Wear_Location_2022-02-23.xlsx
documentation\Stylistics.docx
documentation\Writing Guide.docx

## mysql Directory

mysql\decimal.h
mysql\errmsg.h
mysql\keycache.h
mysql\m_ctype.h
mysql\m_string.h
mysql\my_alloc.h
mysql\my_attribute.h
mysql\my_config.h
mysql\my_dbug.h
mysql\my_dir.h
mysql\my_getopt.h
mysql\my_global.h
mysql\my_list.h
mysql\my_net.h
mysql\my_no_pthread.h
mysql\my_pthread.h
mysql\my_sys.h
mysql\my_xml.h
mysql\mysql.h
mysql\mysql_com.h
mysql\mysql_embed.h
mysql\mysql_time.h
mysql\mysql_version.h
mysql\mysqld_ername.h
mysql\mysqld_error.h
mysql\raid.h
mysql\sql_common.h
mysql\sql_state.h
mysql\sslopt-case.h
mysql\sslopt-longopts.h
mysql\sslopt-vars.h
mysql\typelib.h

## mysql\ndb Directory

mysql\ndb\mgmapi\mgmapi.h
mysql\ndb\mgmapi\mgmapi_config_parameters.h
mysql\ndb\mgmapi\mgmapi_config_parameters_debug.h
mysql\ndb\mgmapi\mgmapi_debug.h
mysql\ndb\mgmapi\ndb_logevent.h
mysql\ndb\mgmapi\ndbd_exit_codes.h
mysql\ndb\ndb_constants.h
mysql\ndb\ndb_init.h
mysql\ndb\ndb_types.h
mysql\ndb\ndb_version.h
mysql\ndb\ndbapi\Ndb.hpp
mysql\ndb\ndbapi\ndb_cluster_connection.hpp
mysql\ndb\ndbapi\ndb_opt_defaults.h
mysql\ndb\ndbapi\NdbApi.hpp
mysql\ndb\ndbapi\ndbapi_limits.h
mysql\ndb\ndbapi\NdbBlob.hpp
mysql\ndb\ndbapi\NdbDictionary.hpp
mysql\ndb\ndbapi\ndberror.h
mysql\ndb\ndbapi\NdbError.hpp
mysql\ndb\ndbapi\NdbIndexOperation.hpp
mysql\ndb\ndbapi\NdbIndexScanOperation.hpp
mysql\ndb\ndbapi\NdbOperation.hpp
mysql\ndb\ndbapi\NdbPool.hpp
mysql\ndb\ndbapi\NdbRecAttr.hpp
mysql\ndb\ndbapi\NdbReceiver.hpp
mysql\ndb\ndbapi\NdbScanFilter.hpp
mysql\ndb\ndbapi\NdbScanOperation.hpp
mysql\ndb\ndbapi\NdbTransaction.hpp

## nbproject Directory

nbproject\configurations.xml
nbproject\Package-Default.bash
nbproject\project.xml

## nbproject\private Directory

nbproject\private\configurations.xml
nbproject\private\Default.properties
nbproject\private\launcher.properties
nbproject\private\private.xml

## unittests Directory

unittests\Makefile-common
unittests\minunit.h
unittests\mocks.c
unittests\test_act.comm.c
unittests\test_act.comm.c-Makefile

## unittests\CuTest Directory

unittests\CuTest\CuTest.c
unittests\CuTest\CuTest.h
unittests\CuTest\license.txt
unittests\CuTest\make-tests.sh
unittests\CuTest\README.txt
unittests\CuTest\test.helpers.c
unittests\CuTest\test.interpreter.c

## util Directory

util\asciipasswd.c
util\autowiz.c
util\depend
util\hl_events.c
util\hl_events.h
util\Makefile
util\Makefile.in
util\plrtoascii.c
util\rebuildAsciiIndex.c
util\rebuildMailIndex.c
util\scheck
util\shopconv.c
util\sign.c
util\split.c
util\webster.c
util\wld2html.c