# Combination of C and C++ files from https://github.com/github/gitignore/blob/master/
# LuminariMUD project
# Checked by Zusuk

# Special Files
campaign.h

# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf
*.lo
*.slo

# Linker output
*.ilk
*.map
*.exp

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo
*.lai

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# Fortran module files
*.mod
*.smod

/unittests/obj
.vscode/settings.json
# generated source file
/unittests/CuTest/AllTests.c
.vscode/c_cpp_properties.json
vnums.h
mud_options.h
