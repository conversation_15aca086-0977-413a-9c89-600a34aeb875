account.o: account.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h mysql.h utils.h db.h helpers.h perfmon.h handler.h feats.h \
 dg_scripts.h comm.h interpreter.h genmob.h constants.h spells.h screen.h \
 class.h act.h account.h
act.comm.o: act.comm.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h screen.h improved-edit.h dg_scripts.h act.h modify.h hlquest.h
act.comm.do_spec_comm.o: act.comm.do_spec_comm.c act.h utils.h db.h \
 conf.h bool.h structs.h protocol.h sysdep.h lists.h campaign.h helpers.h \
 perfmon.h comm.h handler.h hlquest.h
act.informative.o: act.informative.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h handler.h spells.h screen.h constants.h dg_scripts.h \
 mud_event.h dg_event.h mail.h act.h class.h race.h fight.h modify.h \
 asciimap.h clan.h craft.h wilderness.h quest.h feats.h \
 assign_wpn_armor.h domains_schools.h desc_engine.h crafts.h alchemy.h \
 premadebuilds.h staff_events.h missions.h spec_procs.h transport.h \
 encounters.h deities.h
actionqueues.o: actionqueues.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h actionqueues.h mud_event.h dg_event.h actions.h
actions.o: actions.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 screen.h modify.h handler.h spells.h mud_event.h dg_event.h actions.h \
 act.h domains_schools.h
act.item.o: act.item.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h screen.h \
 interpreter.h handler.h spells.h constants.h dg_scripts.h oasis.h help.h \
 act.h quest.h spec_procs.h clan.h mud_event.h dg_event.h hlquest.h \
 fight.h mudlim.h actions.h traps.h assign_wpn_armor.h spec_abilities.h \
 item.h feats.h alchemy.h mysql.h treasure.h crafts.h hunts.h class.h \
 spell_prep.h
act.movement.o: act.movement.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h handler.h spells.h house.h constants.h dg_scripts.h act.h \
 fight.h oasis.h help.h spec_procs.h mud_event.h dg_event.h hlquest.h \
 mudlim.h wilderness.h actions.h traps.h spell_prep.h trails.h \
 assign_wpn_armor.h encounters.h hunts.h class.h transport.h
act.offensive.o: act.offensive.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h handler.h spells.h act.h fight.h mud_event.h dg_event.h \
 constants.h spec_procs.h class.h mudlim.h actions.h actionqueues.h \
 assign_wpn_armor.h feats.h missions.h domains_schools.h encounters.h
act.other.o: act.other.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h screen.h house.h constants.h dg_scripts.h act.h \
 spec_procs.h class.h fight.h mail.h shop.h quest.h modify.h race.h \
 clan.h mud_event.h dg_event.h craft.h treasure.h mudlim.h \
 spec_abilities.h actions.h feats.h assign_wpn_armor.h item.h oasis.h \
 help.h domains_schools.h spell_prep.h premadebuilds.h staff_events.h \
 account.h
act.social.o: act.social.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h screen.h spells.h act.h
act.wizard.o: act.wizard.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h house.h screen.h constants.h oasis.h help.h \
 dg_scripts.h shop.h act.h mysql.h genzon.h class.h genolc.h genobj.h \
 race.h fight.h modify.h quest.h ban.h mud_event.h dg_event.h clan.h \
 craft.h hlquest.h mudlim.h spec_abilities.h wilderness.h feats.h \
 assign_wpn_armor.h item.h domains_schools.h crafts.h account.h alchemy.h \
 premadebuilds.h missions.h deities.h kdtree.h
aedit.o: aedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h interpreter.h handler.h \
 comm.h oasis.h help.h screen.h constants.h genolc.h act.h
alchemy.o: alchemy.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h spells.h handler.h \
 constants.h interpreter.h dg_scripts.h modify.h feats.h class.h \
 mud_event.h dg_event.h assign_wpn_armor.h domains_schools.h spell_prep.h \
 alchemy.h actions.h act.h fight.h
asciimap.o: asciimap.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h house.h constants.h dg_scripts.h asciimap.h \
 wilderness.h modify.h
assign_wpn_armor.o: assign_wpn_armor.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h comm.h utils.h db.h helpers.h perfmon.h \
 mud_event.h dg_event.h actions.h actionqueues.h assign_wpn_armor.h \
 craft.h feats.h constants.h modify.h domains_schools.h spec_abilities.h \
 handler.h spells.h
ban.o: ban.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h ban.h
bardic_performance.o: bardic_performance.c conf.h sysdep.h structs.h \
 bool.h protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h \
 comm.h interpreter.h handler.h mud_event.h dg_event.h spells.h \
 bardic_performance.h fight.h spec_procs.h actions.h feats.h
boards.o: boards.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h boards.h \
 interpreter.h handler.h improved-edit.h modify.h
bsd-snprintf.o: bsd-snprintf.c conf.h sysdep.h
cedit.o: cedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 constants.h genolc.h oasis.h help.h improved-edit.h modify.h
clan.o: clan.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h screen.h improved-edit.h spells.h clan.h mudlim.h
clan_edit.o: clan_edit.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h screen.h genolc.h \
 oasis.h help.h improved-edit.h comm.h interpreter.h modify.h ibt.h \
 clan.h
class.o: class.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h spells.h interpreter.h \
 constants.h act.h handler.h comm.h mud_event.h dg_event.h mudlim.h \
 feats.h class.h assign_wpn_armor.h pfdefaults.h domains_schools.h \
 modify.h spell_prep.h race.h alchemy.h premadebuilds.h
combat_modes.o: combat_modes.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h feats.h \
 comm.h interpreter.h handler.h spells.h class.h mud_event.h dg_event.h \
 combat_modes.h
comm.o: comm.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h house.h oasis.h help.h genolc.h dg_scripts.h dg_event.h \
 screen.h constants.h boards.h act.h ban.h msgedit.h fight.h spells.h \
 modify.h quest.h ibt.h mud_event.h clan.h class.h mail.h new_mail.h \
 mudlim.h actions.h actionqueues.h assign_wpn_armor.h wilderness.h \
 spell_prep.h transport.h hunts.h bardic_performance.h
config.o: config.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h interpreter.h config.h \
 asciimap.h
constants.o: constants.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h interpreter.h \
 spells.h craft.h feats.h domains_schools.h handler.h
craft.o: craft.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h spells.h \
 interpreter.h constants.h handler.h craft.h mud_event.h dg_event.h \
 modify.h treasure.h mudlim.h spec_procs.h item.h quest.h
crafts.o: crafts.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h act.h handler.h \
 interpreter.h screen.h constants.h oasis.h help.h genolc.h spells.h \
 mud_event.h dg_event.h crafts.h item.h
db.o: db.c conf.h sysdep.h structs.h bool.h protocol.h lists.h campaign.h \
 utils.h db.h helpers.h perfmon.h comm.h handler.h spells.h mail.h \
 interpreter.h house.h constants.h oasis.h help.h dg_scripts.h dg_event.h \
 act.h ban.h treasure.h spec_procs.h genzon.h genolc.h genobj.h config.h \
 fight.h modify.h shop.h quest.h ibt.h mud_event.h class.h clan.h \
 msgedit.h craft.h hlquest.h mudlim.h spec_abilities.h perlin.h \
 wilderness.h mysql.h feats.h actionqueues.h domains_schools.h grapple.h \
 race.h spell_prep.h crafts.h trails.h premadebuilds.h encounters.h \
 hunts.h
deities.o: deities.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h spells.h interpreter.h \
 comm.h deities.h domains_schools.h assign_wpn_armor.h
desc_engine.o: desc_engine.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h fight.h comm.h \
 dg_event.h constants.h mysql.h desc_engine.h wilderness.h
dg_comm.o: dg_comm.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h dg_scripts.h utils.h db.h helpers.h perfmon.h comm.h \
 handler.h constants.h
dg_db_scripts.o: dg_db_scripts.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h dg_scripts.h utils.h db.h helpers.h \
 perfmon.h handler.h dg_event.h comm.h constants.h interpreter.h
dg_event.o: dg_event.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h dg_event.h \
 constants.h comm.h mud_event.h
dg_handler.o: dg_handler.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h dg_scripts.h comm.h \
 handler.h spells.h dg_event.h constants.h
dg_misc.o: dg_misc.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h dg_scripts.h comm.h \
 interpreter.h handler.h dg_event.h screen.h spells.h constants.h fight.h \
 mudlim.h
dg_mobcmd.o: dg_mobcmd.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h screen.h \
 dg_scripts.h handler.h interpreter.h comm.h spells.h constants.h \
 genzon.h act.h fight.h shop.h
dg_objcmd.o: dg_objcmd.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h screen.h dg_scripts.h utils.h db.h helpers.h \
 perfmon.h comm.h interpreter.h handler.h constants.h genzon.h fight.h
dg_olc.o: dg_olc.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h genolc.h \
 interpreter.h oasis.h help.h dg_olc.h dg_scripts.h dg_event.h genzon.h \
 constants.h modify.h
dg_scripts.o: dg_scripts.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h dg_scripts.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h handler.h dg_event.h screen.h constants.h spells.h oasis.h \
 help.h genzon.h act.h modify.h
dg_triggers.o: dg_triggers.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h dg_scripts.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h handler.h oasis.h help.h constants.h spells.h act.h \
 modify.h
dg_variables.o: dg_variables.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h dg_scripts.h utils.h db.h helpers.h \
 perfmon.h comm.h interpreter.h handler.h dg_event.h fight.h screen.h \
 constants.h spells.h oasis.h help.h class.h quest.h act.h genobj.h \
 race.h clan.h mudlim.h feats.h
dg_wldcmd.o: dg_wldcmd.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h screen.h dg_scripts.h utils.h db.h helpers.h \
 perfmon.h comm.h interpreter.h handler.h constants.h genzon.h fight.h
domain_powers.o: domain_powers.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h screen.h handler.h spells.h domains_schools.h \
 assign_wpn_armor.h mud_event.h dg_event.h actions.h fight.h act.h
domains_schools.o: domains_schools.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h spells.h feats.h domains_schools.h assign_wpn_armor.h \
 screen.h modify.h class.h
encounters.o: encounters.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h oasis.h \
 help.h screen.h interpreter.h modify.h spells.h feats.h class.h \
 handler.h constants.h assign_wpn_armor.h domains_schools.h spell_prep.h \
 alchemy.h race.h encounters.h dg_scripts.h prefedit.h mud_event.h \
 dg_event.h act.h
feats.o: feats.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h spells.h handler.h \
 constants.h interpreter.h dg_scripts.h modify.h feats.h class.h \
 mud_event.h dg_event.h assign_wpn_armor.h domains_schools.h spell_prep.h
fight.o: fight.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h handler.h \
 interpreter.h spells.h screen.h constants.h dg_scripts.h act.h class.h \
 fight.h shop.h quest.h mud_event.h dg_event.h spec_procs.h clan.h \
 treasure.h mudlim.h spec_abilities.h feats.h actions.h actionqueues.h \
 craft.h assign_wpn_armor.h grapple.h alchemy.h missions.h hunts.h \
 domains_schools.h staff_events.h
gain.o: gain.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h oasis.h help.h \
 screen.h interpreter.h modify.h spells.h
genmob.o: genmob.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h shop.h handler.h genolc.h \
 genmob.h genzon.h dg_olc.h dg_scripts.h spells.h
genobj.o: genobj.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h shop.h constants.h genolc.h \
 genobj.h genzon.h dg_olc.h dg_scripts.h handler.h interpreter.h boards.h \
 craft.h
genolc.o: genolc.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h handler.h comm.h shop.h \
 oasis.h help.h genolc.h genwld.h genmob.h genshp.h genzon.h genobj.h \
 dg_olc.h dg_scripts.h constants.h interpreter.h act.h modify.h quest.h \
 craft.h
genqst.o: genqst.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h quest.h genolc.h oasis.h \
 help.h genzon.h
genshp.o: genshp.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h shop.h genolc.h genshp.h \
 genzon.h
genwld.o: genwld.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h handler.h comm.h genolc.h \
 genwld.h genzon.h shop.h dg_olc.h dg_scripts.h mud_event.h dg_event.h \
 wilderness.h
genzon.o: genzon.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h genolc.h genzon.h \
 dg_scripts.h
graph.o: graph.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h act.h constants.h graph.h fight.h spec_procs.h \
 mud_event.h dg_event.h actions.h wilderness.h shop.h
grapple.o: grapple.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h act.h fight.h mud_event.h dg_event.h constants.h \
 spec_procs.h class.h mudlim.h actions.h actionqueues.h \
 assign_wpn_armor.h feats.h grapple.h
handler.o: handler.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h handler.h screen.h \
 interpreter.h spells.h dg_scripts.h act.h class.h fight.h quest.h \
 mud_event.h dg_event.h wilderness.h actionqueues.h constants.h \
 spec_abilities.h
hedit.o: hedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 boards.h oasis.h help.h genolc.h genzon.h handler.h improved-edit.h \
 act.h hedit.h modify.h mysql.h
help.o: help.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h modify.h comm.h \
 interpreter.h mysql.h help.h feats.h spells.h class.h race.h alchemy.h
helpers.o: helpers.c helpers.h
hlqedit.o: hlqedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h comm.h utils.h db.h helpers.h perfmon.h boards.h handler.h \
 oasis.h help.h interpreter.h constants.h hlquest.h spells.h class.h \
 genzon.h genolc.h genmob.h improved-edit.h modify.h
hlquest.o: hlquest.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h hlquest.h spells.h race.h class.h fight.h act.h constants.h \
 mud_event.h dg_event.h actions.h spell_prep.h
house.o: house.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h handler.h \
 interpreter.h house.h constants.h modify.h mysql.h clan.h act.h \
 dg_scripts.h
hsedit.o: hsedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h comm.h utils.h db.h helpers.h perfmon.h handler.h \
 interpreter.h boards.h oasis.h help.h genolc.h genzon.h house.h screen.h
hunts.o: hunts.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h oasis.h help.h \
 screen.h handler.h constants.h interpreter.h race.h wilderness.h hunts.h \
 act.h spec_abilities.h assign_wpn_armor.h
ibt.o: ibt.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h handler.h \
 interpreter.h constants.h screen.h act.h ibt.h oasis.h help.h \
 improved-edit.h modify.h
improved-edit.o: improved-edit.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h improved-edit.h dg_scripts.h modify.h
interpreter.o: interpreter.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 spells.h handler.h mail.h screen.h genolc.h oasis.h help.h \
 improved-edit.h dg_scripts.h constants.h act.h ban.h class.h graph.h \
 hedit.h house.h config.h modify.h quest.h hlquest.h asciimap.h \
 prefedit.h ibt.h mud_event.h dg_event.h race.h clan.h craft.h treasure.h \
 feats.h actions.h actionqueues.h combat_modes.h traps.h \
 domains_schools.h grapple.h assign_wpn_armor.h bardic_performance.h \
 spell_prep.h crafts.h new_mail.h alchemy.h staff_events.h \
 premadebuilds.h missions.h transport.h hunts.h fight.h
kdtree.o: kdtree.c kdtree.h
limits.o: limits.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h spells.h comm.h handler.h \
 interpreter.h dg_scripts.h class.h fight.h screen.h mud_event.h \
 dg_event.h mudlim.h act.h actions.h domains_schools.h grapple.h \
 constants.h alchemy.h staff_events.h missions.h account.h psionics.h
lists.o: lists.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h dg_event.h
magic.o: magic.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h spells.h handler.h \
 interpreter.h constants.h dg_scripts.h class.h fight.h mud_event.h \
 dg_event.h act.h mudlim.h oasis.h help.h assign_wpn_armor.h \
 domains_schools.h feats.h race.h alchemy.h missions.h psionics.h \
 combat_modes.h
mail.o: mail.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h mail.h modify.h mudlim.h
medit.o: medit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h interpreter.h comm.h \
 spells.h shop.h genolc.h genmob.h genzon.h genshp.h oasis.h help.h \
 handler.h constants.h improved-edit.h dg_olc.h dg_scripts.h screen.h \
 fight.h race.h class.h modify.h
missions.o: missions.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h screen.h constants.h dg_scripts.h mud_event.h \
 dg_event.h mail.h act.h class.h race.h fight.h modify.h asciimap.h \
 clan.h craft.h wilderness.h quest.h feats.h assign_wpn_armor.h \
 domains_schools.h desc_engine.h crafts.h alchemy.h premadebuilds.h \
 missions.h random_names.h spec_procs.h oasis.h help.h mudlim.h genmob.h \
 treasure.h hunts.h
mobact.o: mobact.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h constants.h act.h graph.h fight.h spec_procs.h \
 mud_event.h dg_event.h modify.h mobact.h shop.h quest.h dg_scripts.h
modify.o: modify.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h interpreter.h handler.h \
 comm.h spells.h mail.h boards.h improved-edit.h oasis.h help.h class.h \
 dg_scripts.h modify.h quest.h ibt.h constants.h mysql/mysql.h \
 mysql/mysql_version.h mysql/mysql_com.h mysql/mysql_time.h \
 mysql/typelib.h mysql/my_alloc.h mysql/my_list.h feats.h
msgedit.o: msgedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h screen.h spells.h \
 msgedit.h oasis.h help.h genolc.h interpreter.h modify.h
mud_event.o: mud_event.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h dg_event.h \
 constants.h comm.h mud_event.h handler.h wilderness.h quest.h mysql.h \
 act.h
mysql.o: mysql.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h modify.h mysql.h \
 wilderness.h mud_event.h dg_event.h
new_mail.o: new_mail.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h constants.h spec_procs.h feats.h oasis.h help.h \
 house.h dg_scripts.h clan.h mysql.h modify.h
oasis.o: oasis.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h interpreter.h comm.h shop.h \
 genolc.h genmob.h genshp.h genzon.h genwld.h genobj.h oasis.h help.h \
 screen.h dg_olc.h dg_scripts.h act.h handler.h quest.h ibt.h msgedit.h \
 crafts.h
oasis_copy.o: oasis_copy.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h shop.h genshp.h genolc.h genzon.h genwld.h oasis.h help.h \
 improved-edit.h constants.h dg_scripts.h wilderness.h quest.h
oasis_delete.o: oasis_delete.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h handler.h genolc.h oasis.h help.h improved-edit.h
oasis_list.o: oasis_list.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h genolc.h oasis.h help.h improved-edit.h shop.h screen.h \
 constants.h dg_scripts.h quest.h modify.h spells.h race.h genzon.h \
 class.h genshp.h wilderness.h assign_wpn_armor.h
objsave.o: objsave.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h handler.h \
 interpreter.h spells.h act.h class.h config.h modify.h genolc.h craft.h \
 spec_abilities.h mysql.h
oedit.o: oedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 spells.h boards.h constants.h shop.h genolc.h genobj.h genzon.h oasis.h \
 help.h improved-edit.h dg_olc.h dg_scripts.h fight.h modify.h clan.h \
 craft.h spec_abilities.h feats.h assign_wpn_armor.h domains_schools.h \
 treasure.h act.h handler.h
perlin.o: perlin.c perlin.h
players.o: players.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h handler.h pfdefaults.h \
 dg_scripts.h comm.h interpreter.h mysql.h genolc.h config.h quest.h \
 spells.h clan.h mud_event.h dg_event.h craft.h spell_prep.h alchemy.h \
 templates.h premadebuilds.h missions.h
prefedit.o: prefedit.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h comm.h utils.h db.h helpers.h perfmon.h handler.h \
 interpreter.h oasis.h help.h prefedit.h screen.h encounters.h
premadebuilds.o: premadebuilds.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 modify.h screen.h spells.h handler.h interpreter.h class.h race.h \
 spec_procs.h mud_event.h dg_event.h feats.h spec_abilities.h \
 assign_wpn_armor.h wilderness.h domains_schools.h constants.h \
 dg_scripts.h templates.h oasis.h help.h spell_prep.h premadebuilds.h \
 alchemy.h
protocol.o: protocol.c protocol.h conf.h sysdep.h structs.h bool.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h screen.h improved-edit.h dg_scripts.h act.h modify.h
psionics.o: psionics.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h interpreter.h \
 spells.h handler.h comm.h dg_scripts.h fight.h constants.h mud_event.h \
 dg_event.h spec_procs.h class.h actions.h assign_wpn_armor.h \
 domains_schools.h grapple.h spell_prep.h alchemy.h missions.h psionics.h \
 act.h
qedit.o: qedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h oasis.h help.h \
 improved-edit.h screen.h genolc.h genzon.h interpreter.h modify.h \
 quest.h missions.h
quest.o: quest.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h interpreter.h handler.h \
 comm.h screen.h quest.h act.h mudlim.h mud_event.h dg_event.h missions.h \
 house.h dg_scripts.h
race.o: race.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h spells.h interpreter.h \
 constants.h act.h handler.h comm.h race.h feats.h class.h
random.o: random.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h
random_names.o: random_names.c random_names.h
rank.o: rank.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h screen.h act.h
redit.o: redit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 boards.h genolc.h genwld.h genzon.h oasis.h help.h improved-edit.h \
 dg_olc.h dg_scripts.h constants.h modify.h wilderness.h trails.h
sedit.o: sedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h shop.h \
 genolc.h genshp.h genzon.h oasis.h help.h constants.h
shop.o: shop.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h handler.h \
 interpreter.h shop.h genshp.h constants.h act.h modify.h spells.h \
 screen.h race.h spec_procs.h mudlim.h item.h
spec_abilities.o: spec_abilities.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 dg_event.h spells.h handler.h interpreter.h constants.h dg_scripts.h \
 class.h fight.h mud_event.h act.h mudlim.h oasis.h help.h \
 assign_wpn_armor.h feats.h race.h spec_abilities.h domains_schools.h
spec_assign.o: spec_assign.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h interpreter.h \
 spec_procs.h spells.h ban.h boards.h mail.h treasure.h missions.h \
 hunts.h
spec_procs.o: spec_procs.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h constants.h act.h spec_procs.h class.h fight.h \
 modify.h house.h clan.h mudlim.h graph.h dg_scripts.h mud_event.h \
 dg_event.h actions.h assign_wpn_armor.h domains_schools.h feats.h \
 spell_prep.h item.h alchemy.h treasure.h mobact.h
specs.artifacts.o: specs.artifacts.c
spellbook_scroll.o: spellbook_scroll.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h \
 interpreter.h spells.h comm.h mud_event.h dg_event.h constants.h act.h \
 handler.h spec_procs.h spell_prep.h item.h
spell_parser.o: spell_parser.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h \
 interpreter.h spells.h handler.h comm.h dg_scripts.h fight.h constants.h \
 mud_event.h dg_event.h spec_procs.h class.h actions.h assign_wpn_armor.h \
 domains_schools.h grapple.h spell_prep.h alchemy.h missions.h psionics.h \
 act.h
spell_prep.o: spell_prep.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h interpreter.h comm.h \
 handler.h constants.h spec_procs.h spells.h mud_event.h dg_event.h \
 class.h spell_prep.h domains_schools.h
spells.o: spells.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h spells.h handler.h \
 constants.h interpreter.h dg_scripts.h act.h fight.h mud_event.h \
 dg_event.h house.h screen.h craft.h mudlim.h item.h domains_schools.h \
 oasis.h help.h genzon.h psionics.h assign_wpn_armor.h actions.h
staff_events.o: staff_events.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 interpreter.h handler.h screen.h wilderness.h dg_scripts.h \
 staff_events.h spec_procs.h spells.h
study.o: study.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h oasis.h help.h \
 screen.h interpreter.h modify.h spells.h feats.h class.h handler.h \
 constants.h assign_wpn_armor.h domains_schools.h spell_prep.h alchemy.h \
 race.h premadebuilds.h psionics.h
tedit.o: tedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h interpreter.h comm.h \
 genolc.h oasis.h help.h improved-edit.h modify.h
templates.o: templates.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h modify.h \
 screen.h spells.h handler.h interpreter.h class.h race.h spec_procs.h \
 mud_event.h dg_event.h feats.h spec_abilities.h assign_wpn_armor.h \
 wilderness.h domains_schools.h constants.h dg_scripts.h templates.h \
 mysql.h oasis.h help.h
trade.o: trade.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h interpreter.h handler.h \
 comm.h race.h spells.h trade.h
transport.o: transport.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h oasis.h \
 help.h screen.h interpreter.h modify.h spells.h feats.h class.h \
 handler.h constants.h assign_wpn_armor.h domains_schools.h spell_prep.h \
 alchemy.h race.h transport.h dg_scripts.h wilderness.h
traps.o: traps.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h handler.h mud_event.h \
 dg_event.h actions.h mudlim.h fight.h spells.h traps.h
treasure.o: treasure.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h constants.h dg_scripts.h treasure.h craft.h \
 assign_wpn_armor.h oasis.h help.h item.h staff_events.h feats.h
treasure_const.o: treasure_const.c conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h \
 interpreter.h treasure.h
utils.o: utils.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h modify.h screen.h \
 spells.h handler.h interpreter.h class.h race.h act.h spec_procs.h \
 mud_event.h dg_event.h feats.h spec_abilities.h assign_wpn_armor.h \
 wilderness.h domains_schools.h constants.h dg_scripts.h alchemy.h \
 premadebuilds.h craft.h fight.h missions.h psionics.h
weather.o: weather.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h
wilderness.o: wilderness.c perlin.h conf.h sysdep.h structs.h bool.h \
 protocol.h lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h \
 constants.h mud_event.h dg_event.h wilderness.h kdtree.h mysql.h \
 desc_engine.h
zedit.o: zedit.c conf.h sysdep.h structs.h bool.h protocol.h lists.h \
 campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 constants.h genolc.h genzon.h genmob.h oasis.h help.h dg_scripts.h \
 handler.h
zmalloc.o: zmalloc.c
zone_procs.o: zone_procs.c conf.h sysdep.h structs.h bool.h protocol.h \
 lists.h campaign.h utils.h db.h helpers.h perfmon.h comm.h interpreter.h \
 handler.h spells.h act.h spec_procs.h fight.h graph.h mud_event.h \
 dg_event.h actions.h domains_schools.h spec_abilities.h treasure.h \
 mobact.h dg_scripts.h staff_events.h
