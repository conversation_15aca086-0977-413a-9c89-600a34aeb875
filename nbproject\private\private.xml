

<?xml version="1.0" encoding="UTF-8"?>
<project-private xmlns="http://www.netbeans.org/ns/project-private/1">
    <code-assistance-data xmlns="http://www.netbeans.org/ns/make-project-private/1">
        <code-model-enabled>true</code-model-enabled>
    </code-assistance-data>
    <data xmlns="http://www.netbeans.org/ns/make-project-private/1">
        <activeConfTypeElem>0</activeConfTypeElem>
        <activeConfIndexElem>0</activeConfIndexElem>
    </data>
    <editor-bookmarks xmlns="http://www.netbeans.org/ns/editor-bookmarks/1"/>
    <editor-bookmarks xmlns="http://www.netbeans.org/ns/editor-bookmarks/2" lastBookmarkId="0"/>
    <open-files xmlns="http://www.netbeans.org/ns/projectui-open-files/2">
        <group>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/mysql.c</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/wilderness.c</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/structs.h</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/mysql.h</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/desc_engine.c</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/utils.c</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/utils.h</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/desc_engine.h</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/wilderness.h</file>
            <file>file:/D:/Users/<USER>/Documents/NetBeansProjects/luminari/constants.h</file>
        </group>
    </open-files>
</project-private>
