

<?xml version="1.0" encoding="UTF-8"?>
<configurationDescriptor version="80">
  <logicalFolder name="root" displayName="root" projectFiles="true" kind="ROOT">
    <df name="trunk" root=".">
      <df name="rtree">
        <in>card.c</in>
        <in>card.h</in>
        <in>index.c</in>
        <in>node.c</in>
        <in>rTreeIndex.h</in>
        <in>rectangle.c</in>
        <in>split_l.c</in>
        <in>split_l.h</in>
      </df>
      <df name="util">
        <in>asciipasswd.c</in>
        <in>autowiz.c</in>
        <in>hl_events.c</in>
        <in>hl_events.h</in>
        <in>plrtoascii.c</in>
        <in>rebuildAsciiIndex.c</in>
        <in>rebuildMailIndex.c</in>
        <in>shopconv.c</in>
        <in>sign.c</in>
        <in>split.c</in>
        <in>webster.c</in>
        <in>wld2html.c</in>
      </df>
      <in>account.c</in>
      <in>act.comm.c</in>
      <in>act.h</in>
      <in>act.informative.c</in>
      <in>act.item.c</in>
      <in>act.movement.c</in>
      <in>act.offensive.c</in>
      <in>act.other.c</in>
      <in>act.social.c</in>
      <in>act.wizard.c</in>
      <in>actionqueues.c</in>
      <in>actionqueues.h</in>
      <in>actions.c</in>
      <in>actions.h</in>
      <in>aedit.c</in>
      <in>asciimap.c</in>
      <in>asciimap.h</in>
      <in>assign_wpn_armor.c</in>
      <in>assign_wpn_armor.h</in>
      <in>ban.c</in>
      <in>ban.h</in>
      <in>bardic_performance.c</in>
      <in>bardic_performance.h</in>
      <in>boards.c</in>
      <in>boards.h</in>
      <in>bsd-snprintf.c</in>
      <in>bsd-snprintf.h</in>
      <in>cedit.c</in>
      <in>clan.c</in>
      <in>clan.h</in>
      <in>clan_edit.c</in>
      <in>class.c</in>
      <in>class.h</in>
      <in>combat_modes.c</in>
      <in>combat_modes.h</in>
      <in>comm.c</in>
      <in>comm.h</in>
      <in>conf.h</in>
      <in>config.c</in>
      <in>config.h</in>
      <in>constants.c</in>
      <in>constants.h</in>
      <in>craft.c</in>
      <in>craft.h</in>
      <in>db.c</in>
      <in>db.h</in>
      <in>desc_engine.c</in>
      <in>dg_comm.c</in>
      <in>dg_db_scripts.c</in>
      <in>dg_event.c</in>
      <in>dg_event.h</in>
      <in>dg_handler.c</in>
      <in>dg_misc.c</in>
      <in>dg_mobcmd.c</in>
      <in>dg_objcmd.c</in>
      <in>dg_olc.c</in>
      <in>dg_olc.h</in>
      <in>dg_scripts.c</in>
      <in>dg_scripts.h</in>
      <in>dg_triggers.c</in>
      <in>dg_variables.c</in>
      <in>dg_wldcmd.c</in>
      <in>domain_powers.c</in>
      <in>domains_schools.c</in>
      <in>domains_schools.h</in>
      <in>feats.c</in>
      <in>feats.h</in>
      <in>fight.c</in>
      <in>fight.h</in>
      <in>gain.c</in>
      <in>genmob.c</in>
      <in>genmob.h</in>
      <in>genobj.c</in>
      <in>genobj.h</in>
      <in>genolc.c</in>
      <in>genolc.h</in>
      <in>genqst.c</in>
      <in>genshp.c</in>
      <in>genshp.h</in>
      <in>genwld.c</in>
      <in>genwld.h</in>
      <in>genzon.c</in>
      <in>genzon.h</in>
      <in>graph.c</in>
      <in>graph.h</in>
      <in>grapple.c</in>
      <in>grapple.h</in>
      <in>handler.c</in>
      <in>handler.h</in>
      <in>hedit.c</in>
      <in>hedit.h</in>
      <in>help.c</in>
      <in>help.h</in>
      <in>hlqedit.c</in>
      <in>hlquest.c</in>
      <in>hlquest.h</in>
      <in>house.c</in>
      <in>house.h</in>
      <in>ibt.c</in>
      <in>ibt.h</in>
      <in>improved-edit.c</in>
      <in>improved-edit.h</in>
      <in>interpreter.c</in>
      <in>interpreter.h</in>
      <in>item.h</in>
      <in>kdtree.c</in>
      <in>kdtree.h</in>
      <in>limits.c</in>
      <in>lists.c</in>
      <in>lists.h</in>
      <in>magic.c</in>
      <in>mail.c</in>
      <in>mail.h</in>
      <in>medit.c</in>
      <in>memorize.c</in>
      <in>mobact.c</in>
      <in>mobact.h</in>
      <in>modify.c</in>
      <in>modify.h</in>
      <in>msgedit.c</in>
      <in>msgedit.h</in>
      <in>mud_event.c</in>
      <in>mud_event.h</in>
      <in>mudlim.h</in>
      <in>mysql.c</in>
      <in>mysql.h</in>
      <in>oasis.c</in>
      <in>oasis.h</in>
      <in>oasis_copy.c</in>
      <in>oasis_delete.c</in>
      <in>oasis_list.c</in>
      <in>objsave.c</in>
      <in>oedit.c</in>
      <in>perlin.c</in>
      <in>perlin.h</in>
      <in>pfdefaults.h</in>
      <in>players.c</in>
      <in>prefedit.c</in>
      <in>prefedit.h</in>
      <in>protocol.c</in>
      <in>protocol.h</in>
      <in>qedit.c</in>
      <in>quest.c</in>
      <in>quest.h</in>
      <in>race.c</in>
      <in>race.h</in>
      <in>races_ext.c</in>
      <in>random.c</in>
      <in>redit.c</in>
      <in>screen.h</in>
      <in>sedit.c</in>
      <in>shop.c</in>
      <in>shop.h</in>
      <in>spec_abilities.c</in>
      <in>spec_abilities.h</in>
      <in>spec_assign.c</in>
      <in>spec_procs.c</in>
      <in>spec_procs.h</in>
      <in>spell_parser.c</in>
      <in>spells.c</in>
      <in>spells.h</in>
      <in>structs.h</in>
      <in>study.c</in>
      <in>sysdep.h</in>
      <in>tedit.c</in>
      <in>telnet.h</in>
      <in>trade.c</in>
      <in>trade.h</in>
      <in>traps.c</in>
      <in>traps.h</in>
      <in>treasure.c</in>
      <in>treasure.h</in>
      <in>treasure_const.c</in>
      <in>utils.c</in>
      <in>utils.h</in>
      <in>weather.c</in>
      <in>wilderness.c</in>
      <in>wilderness.h</in>
      <in>zedit.c</in>
      <in>zmalloc.c</in>
      <in>zmalloc.h</in>
      <in>zone_procs.c</in>
    </df>
    <logicalFolder name="ExternalFiles"
                   displayName="Important Files"
                   projectFiles="false"
                   kind="IMPORTANT_FILES_FOLDER">
      <itemPath>Makefile</itemPath>
    </logicalFolder>
  </logicalFolder>
  <sourceFolderFilter>^(nbproject)$</sourceFolderFilter>
  <sourceRootList>
    <Elem>.</Elem>
  </sourceRootList>
  <projectmakefile>Makefile</projectmakefile>
  <confs>
    <conf name="Default" type="0">
      <toolsSet>
        <remote-sources-mode>LOCAL_SOURCES</remote-sources-mode>
        <compilerSet>default</compilerSet>
      </toolsSet>
      <makefileType>
        <makeTool>
          <buildCommandWorkingDir>.</buildCommandWorkingDir>
          <buildCommand>${MAKE} -f Makefile</buildCommand>
          <cleanCommand>${MAKE} -f Makefile clean</cleanCommand>
          <executablePath></executablePath>
          <cTool>
            <incDir>
              <pElem></pElem>
            </incDir>
          </cTool>
        </makeTool>
      </makefileType>
      <item path="act.comm.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="act.informative.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="act.item.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="act.offensive.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="act.other.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="act.social.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="act.wizard.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="actionqueues.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="actionqueues.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="actions.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="aedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="asciimap.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="asciimap.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="ban.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="ban.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="boards.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="boards.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="bsd-snprintf.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="bsd-snprintf.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="cedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="clan.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="clan_edit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="class.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="combat_modes.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="combat_modes.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="comm.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="config.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="config.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="constants.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="craft.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="craft.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="db.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_comm.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_db_scripts.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_event.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_handler.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_misc.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_mobcmd.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_objcmd.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_olc.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_olc.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="dg_scripts.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="dg_triggers.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="fight.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="gain.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="genmob.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="genmob.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="genobj.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="genolc.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="genolc.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="genqst.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="genshp.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="genshp.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="genwld.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="genwld.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="genzon.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="graph.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="graph.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="handler.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="hedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="hedit.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="hlqedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="hlquest.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="house.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="ibt.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="ibt.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="interpreter.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="kdtree.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="kdtree.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="limits.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="lists.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="magic.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="mail.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="mail.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="medit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="memorize.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="mobact.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="mobact.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="modify.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="msgedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="msgedit.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="mud_event.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="mysql.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="mysql.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="oasis.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="oasis_copy.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="oasis_delete.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="oasis_list.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="objsave.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="oedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="perlin.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="perlin.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="pfdefaults.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="players.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="prefedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="prefedit.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="protocol.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="qedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="quest.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="race.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="random.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="redit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="rtree/card.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="rtree/card.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="rtree/index.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="rtree/node.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="rtree/rTreeIndex.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="rtree/rectangle.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="rtree/split_l.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="rtree/split_l.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="sedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="shop.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="shop.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="spec_abilities.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="spec_abilities.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="spec_assign.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="spec_procs.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="spell_parser.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="spells.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="study.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="tedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="telnet.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="treasure.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="treasure.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="treasure_const.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/asciipasswd.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/autowiz.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/hl_events.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/hl_events.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="util/plrtoascii.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/rebuildAsciiIndex.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/rebuildMailIndex.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/shopconv.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/sign.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/split.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/webster.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="util/wld2html.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="utils.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="weather.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="wilderness.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="zedit.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="zmalloc.c" ex="true" tool="0" flavor="0">
      </item>
      <item path="zmalloc.h" ex="true" tool="3" flavor="0">
      </item>
      <item path="zone_procs.c" ex="true" tool="0" flavor="0">
      </item>
    </conf>
  </confs>
</configurationDescriptor>
